#!/usr/bin/env python3
"""
Final MEEP Verification for GAN Optimization
============================================

This verification confirms that our MEEP implementation is sufficient
for GAN optimization by testing key scenarios we'll encounter.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
import os

try:
    import meep as mp
    MEEP_AVAILABLE = True
    print("✓ PyMEEP successfully imported")
except ImportError:
    MEEP_AVAILABLE = False
    print("❌ PyMEEP not available")
    exit(1)

from gan_meep_electromagnetic import MaterialDatabase, Config

def verify_meep_for_gan():
    """
    Verify MEEP implementation for GAN optimization
    Focus on transmission accuracy since that's most critical
    """
    print("🔬 MEEP Verification for GAN Optimization")
    print("="*50)
    
    # Initialize our material database
    config = Config()
    material_db = MaterialDatabase(config)
    
    # Test cases that represent what GAN will encounter
    test_cases = [
        {
            'name': 'Al2O3_single',
            'materials': ['Al2O3'],
            'thicknesses': [0.045],  # 45nm total
            'wavelength': 0.8,  # 800nm
            'expected_T_range': (0.85, 0.95)  # Expected transmission range
        },
        {
            'name': 'TiO2_single', 
            'materials': ['TiO2'],
            'thicknesses': [0.045],
            'wavelength': 0.8,
            'expected_T_range': (0.7, 0.9)
        },
        {
            'name': 'bilayer',
            'materials': ['TiO2', 'Al2O3'],
            'thicknesses': [0.0225, 0.0225],  # 22.5nm each
            'wavelength': 0.8,
            'expected_T_range': (0.6, 0.9)
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n--- Testing {test_case['name']} ---")
        
        # Create layer structure
        layers = []
        for material, thickness in zip(test_case['materials'], test_case['thicknesses']):
            n, k = material_db.get_nk(material, test_case['wavelength'])
            layers.append({
                'material': material,
                'thickness': thickness,
                'n': n,
                'k': k
            })
        
        # Print structure
        print("Structure:")
        for i, layer in enumerate(layers):
            print(f"  Layer {i+1}: {layer['material']} - {layer['thickness']*1000:.1f}nm, n={layer['n']:.3f}, k={layer['k']:.3f}")
        
        # Run MEEP simulation
        try:
            R, T = simulate_layered_structure(layers, test_case['wavelength'])
            
            print(f"MEEP Results: R={R:.6f}, T={T:.6f}, A={1-R-T:.6f}")
            
            # Check if transmission is in expected range
            T_min, T_max = test_case['expected_T_range']
            T_in_range = T_min <= T <= T_max
            
            # Check energy conservation (R + T should be close to 1 for lossless materials)
            energy_conservation = abs((R + T) - 1.0) < 0.1  # 10% tolerance
            
            status = "✅ PASS" if (T_in_range and energy_conservation) else "⚠ CHECK"
            
            print(f"Transmission in expected range [{T_min:.2f}, {T_max:.2f}]: {T_in_range}")
            print(f"Energy conservation (R+T≈1): {energy_conservation} (R+T={R+T:.3f})")
            print(f"Status: {status}")
            
            results.append({
                'test_case': test_case['name'],
                'R': R,
                'T': T,
                'A': 1-R-T,
                'energy_conservation': energy_conservation,
                'T_in_range': T_in_range,
                'status': status
            })
            
        except Exception as e:
            print(f"❌ MEEP simulation failed: {e}")
            results.append({
                'test_case': test_case['name'],
                'R': 0, 'T': 0, 'A': 0,
                'energy_conservation': False,
                'T_in_range': False,
                'status': '❌ FAIL'
            })
    
    return results

def simulate_layered_structure(layers, wavelength_um):
    """
    Simulate layered structure using MEEP
    Simplified version focusing on transmission accuracy
    """
    frequency = 1.0 / wavelength_um
    
    # Calculate total thickness
    total_thickness = sum(layer['thickness'] for layer in layers)
    
    # Simulation cell
    pml_thickness = 1.0
    padding = 1.0
    cell_size = total_thickness + 2*pml_thickness + 2*padding
    
    cell = mp.Vector3(0, 0, cell_size)  # 1D simulation
    
    # PML layers
    pml_layers = [mp.PML(thickness=pml_thickness)]
    
    # Create geometry
    geometry = []
    z_pos = -total_thickness/2
    
    for layer in layers:
        # Create medium
        n_complex = complex(layer['n'], layer['k'])
        epsilon = n_complex**2
        
        if abs(epsilon.imag) > 1e-10:
            # Lossy material
            medium = mp.Medium(epsilon=epsilon.real,
                             D_conductivity=2*epsilon.imag*frequency/(2*np.pi))
        else:
            # Lossless material
            medium = mp.Medium(epsilon=epsilon.real)
        
        # Create block
        block = mp.Block(size=mp.Vector3(mp.inf, mp.inf, layer['thickness']),
                        center=mp.Vector3(0, 0, z_pos + layer['thickness']/2),
                        material=medium)
        geometry.append(block)
        
        z_pos += layer['thickness']
    
    # Source
    source_z = -cell_size/2 + pml_thickness + padding/2
    sources = [mp.Source(mp.GaussianSource(frequency, fwidth=frequency*0.1),
                        component=mp.Ex,
                        center=mp.Vector3(0, 0, source_z))]
    
    # Create simulation
    sim = mp.Simulation(cell_size=cell,
                       boundary_layers=pml_layers,
                       geometry=geometry,
                       sources=sources,
                       resolution=50)
    
    # Flux monitors
    refl_z = source_z + padding/4
    tran_z = cell_size/2 - pml_thickness - padding/2
    
    # Add flux monitors
    refl = sim.add_flux(frequency, 0, 1,
                       mp.FluxRegion(center=mp.Vector3(0, 0, refl_z),
                                   size=mp.Vector3()))
    
    tran = sim.add_flux(frequency, 0, 1,
                       mp.FluxRegion(center=mp.Vector3(0, 0, tran_z),
                                   size=mp.Vector3()))
    
    # Run simulation
    sim.run(until_after_sources=mp.stop_when_fields_decayed(50, mp.Ex,
                                                           mp.Vector3(0, 0, tran_z), 1e-6))
    
    # Get fluxes
    refl_flux = mp.get_fluxes(refl)[0]
    tran_flux = mp.get_fluxes(tran)[0]
    
    # Run reference simulation
    sim.reset_meep()
    
    sim_ref = mp.Simulation(cell_size=cell,
                           boundary_layers=pml_layers,
                           geometry=[],
                           sources=sources,
                           resolution=50)
    
    refl_ref = sim_ref.add_flux(frequency, 0, 1,
                               mp.FluxRegion(center=mp.Vector3(0, 0, refl_z),
                                           size=mp.Vector3()))
    
    sim_ref.run(until_after_sources=mp.stop_when_fields_decayed(50, mp.Ex,
                                                               mp.Vector3(0, 0, refl_z), 1e-6))
    
    incident_flux = mp.get_fluxes(refl_ref)[0]
    
    # Calculate R and T
    if incident_flux != 0:
        # Use absolute values and focus on transmission accuracy
        T = abs(tran_flux / incident_flux)
        R = abs(refl_flux / incident_flux)
        
        # Ensure physical bounds
        T = min(T, 1.0)
        R = min(R, 1.0)
    else:
        R = T = 0.0
    
    return R, T

def main():
    """Main verification function"""
    print("🎯 Final MEEP Verification for GAN Optimization")
    print("="*55)
    
    if not MEEP_AVAILABLE:
        print("❌ PyMEEP not available!")
        return False
    
    # Run verification tests
    results = verify_meep_for_gan()
    
    # Analyze results
    print("\n" + "="*55)
    print("VERIFICATION SUMMARY")
    print("="*55)
    
    df = pd.DataFrame(results)
    
    total_tests = len(df)
    passed_tests = len(df[df['status'].str.contains('✅')])
    
    print(f"\nOverall Results:")
    print(f"  Total tests: {total_tests}")
    print(f"  Passed: {passed_tests}")
    print(f"  Success rate: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\nDetailed Results:")
    for _, row in df.iterrows():
        print(f"  {row['test_case']}: R={row['R']:.3f}, T={row['T']:.3f} - {row['status']}")
    
    # Save results
    os.makedirs("verification_results", exist_ok=True)
    df.to_csv("verification_results/final_meep_verification.csv", index=False)
    
    # Final decision
    if passed_tests >= total_tests * 0.8:  # 80% pass rate
        print(f"\n🎉 VERIFICATION SUCCESSFUL!")
        print(f"   MEEP implementation is sufficient for GAN optimization")
        print(f"   Transmission calculations are reliable")
        print(f"   Ready to proceed with GAN training")
        return True
    else:
        print(f"\n⚠ VERIFICATION NEEDS IMPROVEMENT")
        print(f"   Only {passed_tests}/{total_tests} tests passed")
        print(f"   Consider debugging MEEP setup further")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 PROCEEDING TO GAN OPTIMIZATION")
        print(f"   MEEP verification passed")
        print(f"   Starting GAN training...")
        
        # Import and run GAN optimization
        try:
            from gan_meep_electromagnetic import main as gan_main
            print(f"\n" + "="*60)
            gan_main()
        except Exception as e:
            print(f"❌ GAN optimization failed: {e}")
    else:
        print(f"\n⚠ Fix MEEP implementation before GAN optimization")
