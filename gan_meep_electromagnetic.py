#!/usr/bin/env python3
"""
GAN for Electromagnetic Simulation with Real PyMEEP
==================================================

Complete implementation of GAN architecture for optimizing 45nm material structures
using actual MEEP/PyMEEP library for electromagnetic simulations.

Architecture:
- Generator: Creates 3D material distributions (4 materials: 4nm TiN, TiO2, Al2O3, 30nm TiN)
- Surrogate Simulator: Ultrafast R/T predictor (proxy for FDTD)
- Discriminator: Distinguishes real vs fake structural patterns
- MEEP Simulator: Real electromagnetic simulation for validation and training

Structure (top to bottom):
- 300nm PML (perfectly matched layer)
- 500nm+ reflection detector and light source
- 45nm material structure (optimized by GAN)
- 500nm+ transmission detector
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import time
import os
from scipy import interpolate
import warnings
warnings.filterwarnings('ignore')

# Try to import MEEP - if not available, provide clear error message
try:
    import meep as mp
    MEEP_AVAILABLE = True
    print("✓ PyMEEP successfully imported")
except ImportError:
    MEEP_AVAILABLE = False
    print("⚠ PyMEEP not available. Please install with: conda install -c conda-forge pymeep")

# ====================== Configuration ======================
class Config:
    """Configuration parameters for the GAN electromagnetic simulation"""
    
    # Simulation parameters
    WAVELENGTH_RANGE = (300, 2600)  # nm
    INCIDENT_ANGLES = [15, 30, 45, 60]  # degrees
    TARGET_THICKNESS = 45  # nm
    
    # Structure dimensions
    PML_THICKNESS = 300  # nm
    DETECTOR_DISTANCE = 500  # nm (minimum)
    GRID_SIZE = (100, 50, 100)  # (x, y, z) grid size for material distribution
    
    # Materials (4 materials as specified)
    MATERIALS = ['TiN_4nm', 'TiO2', 'Al2O3', 'TiN_30nm']
    
    # Material data files
    MATERIAL_NK_FILES = {
        'TiN_4nm': 'data/TiN-4nm.xlsx',
        'TiO2': 'data/TiO2.csv',
        'Al2O3': 'data/Al2O3.txt',
        'TiN_30nm': 'data/TiN-30nm.csv'
    }
    
    # Experimental data files
    EXPERIMENTAL_DATA = {
        15: 'data/RT_15degree_SP.csv',
        30: 'data/RT_30degree_SP.csv', 
        45: 'data/RT_45degree_SP.csv',
        60: 'data/RT_60degree_SP.csv'
    }
    
    # Loss function weights
    ALPHA1 = 0.5  # Weight for reflectance loss
    ALPHA2 = 0.5  # Weight for transmittance loss
    
    # Training parameters
    BATCH_SIZE = 16
    LEARNING_RATE = 0.0002
    NUM_EPOCHS = 100
    LATENT_DIM = 100
    
    # Output directory
    OUTPUT_DIR = "results"

# Ensure output directory exists
os.makedirs(Config.OUTPUT_DIR, exist_ok=True)

# ====================== Material Database ======================
class MaterialDatabase:
    """
    Material database for electromagnetic properties
    Handles loading and interpolation of n,k data for all materials
    """
    
    def __init__(self, config):
        self.config = config
        self.materials_nk = {}
        self.load_all_materials()
    
    def load_all_materials(self):
        """Load all material data"""
        print("Loading material database...")
        for material_name, file_path in self.config.MATERIAL_NK_FILES.items():
            try:
                if os.path.exists(file_path):
                    self.load_material_data(material_name, file_path)
                    print(f"✓ Loaded {material_name} from {file_path}")
                else:
                    self.create_default_material_data(material_name)
                    print(f"⚠ Created default data for {material_name} (file not found)")
            except Exception as e:
                print(f"⚠ Error loading {material_name}: {e}")
                self.create_default_material_data(material_name)
    
    def load_material_data(self, name, file_path):
        """Load material n,k data from file"""
        # Load data based on file extension
        if file_path.endswith('.xlsx'):
            data = pd.read_excel(file_path)
        elif file_path.endswith('.csv'):
            data = pd.read_csv(file_path)
        else:  # txt format
            data = pd.read_csv(file_path, sep='\t', header=None, 
                              names=['wavelength', 'n', 'k'])
        
        # Handle different column naming conventions
        if 'wavelength' in data.columns:
            wavelengths = data['wavelength'].values
            n_values = data['n'].values
            k_values = data['k'].values
        else:
            # Assume first three columns are wavelength, n, k
            wavelengths = data.iloc[:, 0].values
            n_values = data.iloc[:, 1].values
            k_values = data.iloc[:, 2].values
        
        # Convert wavelength to micrometers if needed
        if np.max(wavelengths) > 10:  # Assume nm if max > 10
            wavelengths = wavelengths * 1e-3
        
        # Store material data with interpolation functions
        self.materials_nk[name] = {
            'n': interpolate.interp1d(wavelengths, n_values, 
                                      bounds_error=False, fill_value="extrapolate"),
            'k': interpolate.interp1d(wavelengths, k_values, 
                                      bounds_error=False, fill_value="extrapolate")
        }
    
    def create_default_material_data(self, name):
        """Create default material properties when data file is not available"""
        wavelengths = np.linspace(0.3, 2.6, 100)  # 300-2600 nm in micrometers
        
        # Default material properties (approximate values)
        if 'TiN' in name:
            # TiN is a metallic material with moderate n and k for stability
            n_values = np.ones_like(wavelengths) * 2.0
            k_values = np.ones_like(wavelengths) * 0.5
        elif 'TiO2' in name:
            # TiO2 is a high-index dielectric
            n_values = np.ones_like(wavelengths) * 2.4
            k_values = np.zeros_like(wavelengths)
        elif 'Al2O3' in name:
            # Al2O3 is a low-index dielectric
            n_values = np.ones_like(wavelengths) * 1.77
            k_values = np.zeros_like(wavelengths)
        else:
            n_values = np.ones_like(wavelengths) * 1.5
            k_values = np.zeros_like(wavelengths)
        
        # Store with interpolation functions
        self.materials_nk[name] = {
            'n': interpolate.interp1d(wavelengths, n_values, 
                                      bounds_error=False, fill_value="extrapolate"),
            'k': interpolate.interp1d(wavelengths, k_values, 
                                      bounds_error=False, fill_value="extrapolate")
        }
    
    def get_nk(self, material_name, wavelength_um):
        """Get n,k values for a material at given wavelength"""
        if material_name not in self.materials_nk:
            raise ValueError(f"Material {material_name} not found in database")
        
        n = float(self.materials_nk[material_name]['n'](wavelength_um))
        k = float(self.materials_nk[material_name]['k'](wavelength_um))
        return n, k
    
    def get_medium(self, material_name, wavelength_um):
        """Get MEEP medium object for a material"""
        if not MEEP_AVAILABLE:
            raise RuntimeError("PyMEEP not available")
        
        n, k = self.get_nk(material_name, wavelength_um)
        epsilon = (n + 1j*k)**2
        return mp.Medium(epsilon=epsilon.real, D_conductivity=2*epsilon.imag*wavelength_um/(2*np.pi))

# ====================== MEEP Simulator ======================
class MEEPSimulator:
    """
    Real MEEP electromagnetic simulator
    Handles the complete electromagnetic simulation using PyMEEP
    """
    
    def __init__(self, config, material_db):
        self.config = config
        self.material_db = material_db
        
        if not MEEP_AVAILABLE:
            raise RuntimeError("PyMEEP is required for electromagnetic simulation")
    
    def simulate_structure(self, material_distribution, wavelength_nm, angle_deg=0):
        """
        Simulate electromagnetic response of a 3D material structure
        
        Args:
            material_distribution: 3D array (100, 50, 100, 4) with material probabilities
            wavelength_nm: Wavelength in nanometers
            angle_deg: Incident angle in degrees
            
        Returns:
            R, T: Reflectance and transmittance
        """
        wavelength_um = wavelength_nm * 1e-3
        frequency = 1.0 / wavelength_um
        
        # Convert material distribution to dominant material indices
        material_indices = np.argmax(material_distribution, axis=-1)
        
        # Define simulation cell dimensions
        cell_x = 2.0  # μm
        cell_y = 1.0  # μm  
        cell_z = 3.0  # μm (includes PML, detectors, and structure)
        
        cell = mp.Vector3(cell_x, cell_y, cell_z)
        
        # Define PML layers
        pml_layers = [mp.PML(thickness=0.3)]  # 300nm PML
        
        # Create geometry based on material distribution
        geometry = self._create_geometry_from_distribution(material_indices, wavelength_um)
        
        # Define source
        source_z = -cell_z/2 + 0.8  # Position source
        sources = [mp.Source(mp.GaussianSource(frequency, fwidth=frequency*0.1),
                           component=mp.Ex,
                           center=mp.Vector3(0, 0, source_z))]
        
        # Create simulation
        sim = mp.Simulation(cell_size=cell,
                          boundary_layers=pml_layers,
                          geometry=geometry,
                          sources=sources,
                          resolution=20)
        
        # Add flux monitors
        refl_z = source_z + 0.1
        tran_z = cell_z/2 - 0.8
        
        refl = sim.add_flux(frequency, 0, 1,
                          mp.FluxRegion(center=mp.Vector3(0, 0, refl_z),
                                      size=mp.Vector3(cell_x*0.8, cell_y*0.8, 0)))
        
        tran = sim.add_flux(frequency, 0, 1,
                          mp.FluxRegion(center=mp.Vector3(0, 0, tran_z),
                                      size=mp.Vector3(cell_x*0.8, cell_y*0.8, 0)))
        
        # Run simulation
        sim.run(until_after_sources=mp.stop_when_fields_decayed(50, mp.Ex, 
                                                               mp.Vector3(0, 0, tran_z), 1e-6))
        
        # Calculate R and T
        refl_flux = mp.get_fluxes(refl)[0]
        tran_flux = mp.get_fluxes(tran)[0]
        
        # Normalize (run reference simulation for incident flux)
        sim.reset_meep()
        sim.run(until_after_sources=mp.stop_when_fields_decayed(50, mp.Ex,
                                                               mp.Vector3(0, 0, refl_z), 1e-6))
        incident_flux = mp.get_fluxes(refl)[0]
        
        R = -refl_flux / incident_flux if incident_flux != 0 else 0
        T = tran_flux / incident_flux if incident_flux != 0 else 0
        
        return max(0, R), max(0, T)
    
    def _create_geometry_from_distribution(self, material_indices, wavelength_um):
        """Create MEEP geometry from 3D material distribution"""
        geometry = []
        
        # Convert grid indices to physical coordinates
        grid_x, grid_y, grid_z = material_indices.shape
        dx = 2.0 / grid_x  # μm per grid point
        dy = 1.0 / grid_y
        dz = 0.045 / grid_z  # 45nm structure thickness
        
        # Create blocks for each material region
        for i in range(grid_x):
            for j in range(grid_y):
                for k in range(grid_z):
                    material_idx = material_indices[i, j, k]
                    material_name = self.config.MATERIALS[material_idx]
                    
                    # Get material medium
                    medium = self.material_db.get_medium(material_name, wavelength_um)
                    
                    # Calculate position
                    x = (i - grid_x/2 + 0.5) * dx
                    y = (j - grid_y/2 + 0.5) * dy
                    z = (k - grid_z/2 + 0.5) * dz
                    
                    # Create block
                    block = mp.Block(size=mp.Vector3(dx, dy, dz),
                                   center=mp.Vector3(x, y, z),
                                   material=medium)
                    geometry.append(block)
        
        return geometry

# ====================== GAN Architecture ======================

class Generator(nn.Module):
    """
    Generator Network for GAN

    Takes random noise vector and generates 3D material distribution
    Output: 3D material distribution (100, 50, 100, 4) for 4 materials:
    - TiN_4nm (thin film)
    - TiO2
    - Al2O3
    - TiN_30nm
    """

    def __init__(self, latent_dim=100, num_materials=4, grid_size=(100, 50, 100)):
        super(Generator, self).__init__()
        self.latent_dim = latent_dim
        self.num_materials = num_materials
        self.grid_size = grid_size

        # Calculate initial feature map size for upsampling
        self.init_size = (8, 4, 8)

        # Linear layer to project noise to initial feature map
        self.fc = nn.Linear(latent_dim, 512 * self.init_size[0] * self.init_size[1] * self.init_size[2])

        # 3D Transposed Convolutional layers for upsampling
        self.conv_blocks = nn.Sequential(
            # 8x4x8 -> 16x8x16
            nn.ConvTranspose3d(512, 256, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),

            # 16x8x16 -> 32x16x32
            nn.ConvTranspose3d(256, 128, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),

            # 32x16x32 -> 64x32x64
            nn.ConvTranspose3d(128, 64, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),

            # Final layer: output material probabilities
            nn.Conv3d(64, num_materials, kernel_size=3, padding=1),
            nn.Softmax(dim=1)  # Softmax over material dimension
        )

    def forward(self, z):
        """Generate 3D material distribution from noise"""
        # Project noise to initial feature map
        x = self.fc(z)
        x = x.view(x.size(0), 512, self.init_size[0], self.init_size[1], self.init_size[2])

        # Apply convolutional blocks
        x = self.conv_blocks(x)

        # Interpolate to exact target size
        x = F.interpolate(x, size=self.grid_size, mode='trilinear', align_corners=False)

        return x

class Discriminator(nn.Module):
    """
    Discriminator Network for GAN

    Distinguishes between real and fake structural patterns
    Input: 3D material distribution (100, 50, 100, 4)
    Output: Probability that input is real
    """

    def __init__(self, num_materials=4, grid_size=(100, 50, 100)):
        super(Discriminator, self).__init__()
        self.num_materials = num_materials
        self.grid_size = grid_size

        # 3D Convolutional layers for downsampling
        self.conv_blocks = nn.Sequential(
            # 100x50x100 -> 50x25x50
            nn.Conv3d(num_materials, 32, kernel_size=4, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),

            # 50x25x50 -> 25x12x25
            nn.Conv3d(32, 64, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(64),
            nn.LeakyReLU(0.2, inplace=True),

            # 25x12x25 -> 12x6x12
            nn.Conv3d(64, 128, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(128),
            nn.LeakyReLU(0.2, inplace=True),

            # 12x6x12 -> 6x3x6
            nn.Conv3d(128, 256, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(256),
            nn.LeakyReLU(0.2, inplace=True),

            # Global average pooling
            nn.AdaptiveAvgPool3d((1, 1, 1)),
            nn.Flatten()
        )

        # Classification layers
        self.classifier = nn.Sequential(
            nn.Linear(256, 128),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        """Classify input as real or fake"""
        x = self.conv_blocks(x)
        x = self.classifier(x)
        return x

class SurrogateSimulator(nn.Module):
    """
    Surrogate Simulator Network

    Pre-trained neural network serving as ultrafast proxy for FDTD
    Predicts R/T from 3D material maps much faster than full MEEP simulation

    Input: 3D material distribution + wavelength + angle + polarization
    Output: Reflectance and Transmittance (R, T)
    """

    def __init__(self, num_materials=4, grid_size=(100, 50, 100)):
        super(SurrogateSimulator, self).__init__()
        self.num_materials = num_materials
        self.grid_size = grid_size

        # 3D CNN feature extractor for material distribution
        self.material_encoder = nn.Sequential(
            # 100x50x100 -> 50x25x50
            nn.Conv3d(num_materials, 32, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(32),
            nn.ReLU(inplace=True),

            # 50x25x50 -> 25x12x25
            nn.Conv3d(32, 64, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),

            # 25x12x25 -> 12x6x12
            nn.Conv3d(64, 128, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),

            # Global average pooling
            nn.AdaptiveAvgPool3d((1, 1, 1)),
            nn.Flatten()
        )

        # Parameter encoder for wavelength, angle, polarization
        self.param_encoder = nn.Sequential(
            nn.Linear(3, 64),  # wavelength, angle, polarization
            nn.ReLU(inplace=True),
            nn.Linear(64, 128),
            nn.ReLU(inplace=True)
        )

        # Combined feature processor
        self.feature_combiner = nn.Sequential(
            nn.Linear(128 + 128, 512),  # material features + param features
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True)
        )

        # Output heads for R and T
        self.r_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )

        self.t_head = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )

    def forward(self, material_dist, wavelength, angle, polarization):
        """Predict R and T from material distribution and parameters"""
        # Extract material features
        material_features = self.material_encoder(material_dist)

        # Combine parameters
        params = torch.cat([wavelength, angle, polarization], dim=1)
        param_features = self.param_encoder(params)

        # Combine all features
        combined_features = torch.cat([material_features, param_features], dim=1)
        features = self.feature_combiner(combined_features)

        # Predict R and T
        R = self.r_head(features)
        T = self.t_head(features)

        return R, T

# ====================== Experimental Data Loader ======================

class ExperimentalDataLoader:
    """
    Loads experimental R/T data for different angles
    Data format: wavelength(nm), R, T for each angle
    """

    def __init__(self, config):
        self.config = config
        self.experimental_data = {}
        self.load_all_experimental_data()

    def load_all_experimental_data(self):
        """Load experimental data for all angles"""
        print("Loading experimental data...")
        for angle, file_path in self.config.EXPERIMENTAL_DATA.items():
            try:
                if os.path.exists(file_path):
                    data = pd.read_csv(file_path)
                    self.experimental_data[angle] = {
                        'wavelength': data.iloc[:, 0].values,  # nm
                        'R': data.iloc[:, 1].values,
                        'T': data.iloc[:, 2].values
                    }
                    print(f"✓ Loaded experimental data for {angle}° from {file_path}")
                else:
                    print(f"⚠ Experimental data file not found: {file_path}")
                    # Create dummy data for testing
                    wavelengths = np.linspace(300, 2600, 100)
                    self.experimental_data[angle] = {
                        'wavelength': wavelengths,
                        'R': np.random.uniform(0.1, 0.3, len(wavelengths)),
                        'T': np.random.uniform(0.1, 0.3, len(wavelengths))
                    }
            except Exception as e:
                print(f"⚠ Error loading experimental data for {angle}°: {e}")

    def get_rt_at_wavelength(self, angle, wavelength_nm):
        """Get experimental R,T at specific wavelength and angle"""
        if angle not in self.experimental_data:
            raise ValueError(f"No experimental data for angle {angle}°")

        data = self.experimental_data[angle]

        # Interpolate to get R,T at exact wavelength
        r_interp = interpolate.interp1d(data['wavelength'], data['R'],
                                       bounds_error=False, fill_value="extrapolate")
        t_interp = interpolate.interp1d(data['wavelength'], data['T'],
                                       bounds_error=False, fill_value="extrapolate")

        R_exp = float(r_interp(wavelength_nm))
        T_exp = float(t_interp(wavelength_nm))

        return R_exp, T_exp

# ====================== GAN Training Pipeline ======================

class GANTrainer:
    """
    Main GAN training pipeline

    Combines Generator, Discriminator, Surrogate Simulator, and MEEP validation
    Optimizes material structures to match experimental R/T data
    """

    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")

        # Initialize components
        self.material_db = MaterialDatabase(config)
        self.experimental_data = ExperimentalDataLoader(config)

        if MEEP_AVAILABLE:
            self.meep_sim = MEEPSimulator(config, self.material_db)
            print("✓ MEEP simulator initialized")
        else:
            self.meep_sim = None
            print("⚠ MEEP simulator not available")

        # Initialize networks
        self.generator = Generator(
            latent_dim=config.LATENT_DIM,
            num_materials=len(config.MATERIALS),
            grid_size=config.GRID_SIZE
        ).to(self.device)

        self.discriminator = Discriminator(
            num_materials=len(config.MATERIALS),
            grid_size=config.GRID_SIZE
        ).to(self.device)

        self.surrogate = SurrogateSimulator(
            num_materials=len(config.MATERIALS),
            grid_size=config.GRID_SIZE
        ).to(self.device)

        # Initialize optimizers
        self.g_optimizer = optim.Adam(self.generator.parameters(), lr=config.LEARNING_RATE, betas=(0.5, 0.999))
        self.d_optimizer = optim.Adam(self.discriminator.parameters(), lr=config.LEARNING_RATE, betas=(0.5, 0.999))
        self.s_optimizer = optim.Adam(self.surrogate.parameters(), lr=config.LEARNING_RATE)

        # Loss functions
        self.adversarial_loss = nn.BCELoss()
        self.rt_loss = nn.MSELoss()

        # Training history
        self.training_history = {
            'g_losses': [],
            'd_losses': [],
            's_losses': [],
            'physics_losses': [],
            'best_loss': float('inf'),
            'best_structure': None
        }

    def generate_real_patterns(self, batch_size=16):
        """Generate real structural patterns for discriminator training"""
        real_patterns = []

        for _ in range(batch_size):
            # Create structured layered patterns (not random)
            pattern = torch.zeros(len(self.config.MATERIALS), *self.config.GRID_SIZE)

            # Create layered structure with some variation
            layer_height = self.config.GRID_SIZE[2] // len(self.config.MATERIALS)
            for i, material_idx in enumerate(range(len(self.config.MATERIALS))):
                start_z = i * layer_height
                end_z = min((i + 1) * layer_height, self.config.GRID_SIZE[2])

                # Add some spatial variation
                for x in range(self.config.GRID_SIZE[0]):
                    for y in range(self.config.GRID_SIZE[1]):
                        # Add some randomness but keep structure
                        if np.random.random() > 0.1:  # 90% probability
                            pattern[material_idx, x, y, start_z:end_z] = 1.0

            # Add small amount of noise
            noise = torch.randn_like(pattern) * 0.05
            pattern = torch.clamp(pattern + noise, 0, 1)

            # Renormalize to ensure sum=1 across materials
            pattern = F.softmax(pattern, dim=0)

            real_patterns.append(pattern)

        return torch.stack(real_patterns).to(self.device)

    def calculate_physics_loss(self, material_distributions):
        """
        Calculate physics-based loss using experimental data
        Loss = α1 * ||(Rsim - Rexp) / Rexp|| + α2 * ||(Tsim - Texp) / Texp||
        """
        total_loss = 0.0
        num_samples = 0

        for i, material_dist in enumerate(material_distributions):
            # Test multiple wavelengths and angles
            test_wavelengths = [500, 800, 1200, 1600]  # nm
            test_angles = [15, 30, 45, 60]  # degrees

            sample_loss = 0.0
            sample_count = 0

            for wavelength_nm in test_wavelengths:
                for angle_deg in test_angles:
                    try:
                        # Get experimental R,T
                        R_exp, T_exp = self.experimental_data.get_rt_at_wavelength(angle_deg, wavelength_nm)

                        # Use surrogate simulator for fast prediction
                        wavelength_tensor = torch.tensor([[wavelength_nm / 1000.0]], device=self.device)  # Convert to μm
                        angle_tensor = torch.tensor([[angle_deg / 90.0]], device=self.device)  # Normalize
                        polarization_tensor = torch.zeros(1, 1, device=self.device)  # s-polarization

                        R_sim, T_sim = self.surrogate(
                            material_dist.unsqueeze(0),
                            wavelength_tensor,
                            angle_tensor,
                            polarization_tensor
                        )

                        R_sim = R_sim.squeeze()
                        T_sim = T_sim.squeeze()

                        # Calculate relative errors (avoid division by zero)
                        if R_exp > 1e-6:
                            r_error = torch.abs((R_sim - R_exp) / R_exp)
                        else:
                            r_error = torch.abs(R_sim)

                        if T_exp > 1e-6:
                            t_error = torch.abs((T_sim - T_exp) / T_exp)
                        else:
                            t_error = torch.abs(T_sim)

                        # Weighted loss
                        loss = self.config.ALPHA1 * r_error + self.config.ALPHA2 * t_error
                        sample_loss += loss
                        sample_count += 1

                    except Exception as e:
                        print(f"⚠ Error in physics loss calculation: {e}")
                        continue

            if sample_count > 0:
                total_loss += sample_loss / sample_count
                num_samples += 1

        return total_loss / num_samples if num_samples > 0 else torch.tensor(0.0, device=self.device)

    def validate_with_meep(self, material_distribution, wavelength_nm=800, angle_deg=0):
        """Validate structure using full MEEP simulation"""
        if not self.meep_sim:
            print("⚠ MEEP validation not available")
            return 0.0, 0.0

        try:
            # Convert to numpy for MEEP
            material_dist_np = material_distribution.detach().cpu().numpy()
            R, T = self.meep_sim.simulate_structure(material_dist_np, wavelength_nm, angle_deg)
            return R, T
        except Exception as e:
            print(f"⚠ MEEP validation error: {e}")
            return 0.0, 0.0

    def train_gan(self, num_epochs=None):
        """Main GAN training loop"""
        if num_epochs is None:
            num_epochs = self.config.NUM_EPOCHS

        print(f"Starting GAN training for {num_epochs} epochs...")
        print(f"Target: Optimize 45nm structure with 4 materials: {self.config.MATERIALS}")
        print(f"Loss function: α1={self.config.ALPHA1} * |ΔR/R| + α2={self.config.ALPHA2} * |ΔT/T|")

        for epoch in range(num_epochs):
            epoch_start = time.time()

            # =================== Train Discriminator ===================
            self.discriminator.train()
            self.generator.eval()

            # Generate real and fake patterns
            real_patterns = self.generate_real_patterns(batch_size=self.config.BATCH_SIZE)
            real_labels = torch.ones(real_patterns.size(0), 1, device=self.device)

            noise = torch.randn(self.config.BATCH_SIZE, self.config.LATENT_DIM, device=self.device)
            fake_patterns = self.generator(noise)
            fake_labels = torch.zeros(fake_patterns.size(0), 1, device=self.device)

            # Train discriminator
            self.d_optimizer.zero_grad()

            real_pred = self.discriminator(real_patterns)
            d_real_loss = self.adversarial_loss(real_pred, real_labels)

            fake_pred = self.discriminator(fake_patterns.detach())
            d_fake_loss = self.adversarial_loss(fake_pred, fake_labels)

            d_loss = (d_real_loss + d_fake_loss) / 2
            d_loss.backward()
            self.d_optimizer.step()

            # =================== Train Generator ===================
            self.generator.train()
            self.g_optimizer.zero_grad()

            # Generate new fake patterns
            noise = torch.randn(self.config.BATCH_SIZE, self.config.LATENT_DIM, device=self.device)
            fake_patterns = self.generator(noise)
            fake_pred = self.discriminator(fake_patterns)

            # Adversarial loss (fool discriminator)
            g_adversarial_loss = self.adversarial_loss(fake_pred, real_labels)

            # Physics-based loss (match experimental data)
            physics_loss = self.calculate_physics_loss(fake_patterns)

            # Combined generator loss
            g_loss = g_adversarial_loss + 10.0 * physics_loss  # Weight physics loss higher
            g_loss.backward()
            self.g_optimizer.step()

            # =================== Record Progress ===================
            self.training_history['g_losses'].append(g_loss.item())
            self.training_history['d_losses'].append(d_loss.item())
            self.training_history['physics_losses'].append(physics_loss.item())

            # Check for best structure
            if physics_loss.item() < self.training_history['best_loss']:
                self.training_history['best_loss'] = physics_loss.item()
                self.training_history['best_structure'] = fake_patterns[0].clone().cpu()
                print(f"🎯 New best structure found! Loss: {physics_loss.item():.6f}")

            epoch_time = time.time() - epoch_start

            # =================== Progress Report ===================
            if epoch % 10 == 0:
                print(f"Epoch {epoch}/{num_epochs}")
                print(f"  G_loss: {g_loss.item():.4f} (Adv: {g_adversarial_loss.item():.4f}, Phys: {physics_loss.item():.6f})")
                print(f"  D_loss: {d_loss.item():.4f}")
                print(f"  Best loss: {self.training_history['best_loss']:.6f}")
                print(f"  Time: {epoch_time:.2f}s")

                # Save sample structure visualization
                if epoch % 50 == 0:
                    self.save_structure_visualization(fake_patterns[0], epoch)

                    # MEEP validation for best structure
                    if self.training_history['best_structure'] is not None:
                        R_meep, T_meep = self.validate_with_meep(self.training_history['best_structure'])
                        print(f"  MEEP validation - R: {R_meep:.4f}, T: {T_meep:.4f}")

        print("🎉 GAN training completed!")
        self.save_final_results()
        return self.training_history['best_structure']

    def save_structure_visualization(self, structure, epoch):
        """Save visualization of generated structure"""
        structure_np = structure.detach().cpu().numpy()
        dominant_materials = np.argmax(structure_np, axis=0)

        # Create visualization
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))

        # XY slice (middle)
        axes[0, 0].imshow(dominant_materials[:, :, 50], cmap='viridis', aspect='auto')
        axes[0, 0].set_title(f'XY slice (z=50) - Epoch {epoch}')
        axes[0, 0].set_xlabel('Y')
        axes[0, 0].set_ylabel('X')

        # XZ slice (middle)
        axes[0, 1].imshow(dominant_materials[:, 25, :], cmap='viridis', aspect='auto')
        axes[0, 1].set_title(f'XZ slice (y=25) - Epoch {epoch}')
        axes[0, 1].set_xlabel('Z')
        axes[0, 1].set_ylabel('X')

        # YZ slice (middle)
        axes[1, 0].imshow(dominant_materials[50, :, :], cmap='viridis', aspect='auto')
        axes[1, 0].set_title(f'YZ slice (x=50) - Epoch {epoch}')
        axes[1, 0].set_xlabel('Z')
        axes[1, 0].set_ylabel('Y')

        # Material distribution histogram
        material_counts = [np.sum(dominant_materials == i) for i in range(len(self.config.MATERIALS))]
        axes[1, 1].bar(range(len(self.config.MATERIALS)), material_counts)
        axes[1, 1].set_title('Material Distribution')
        axes[1, 1].set_xlabel('Material Index')
        axes[1, 1].set_ylabel('Voxel Count')
        axes[1, 1].set_xticks(range(len(self.config.MATERIALS)))
        axes[1, 1].set_xticklabels(self.config.MATERIALS, rotation=45)

        plt.tight_layout()
        plt.savefig(f"{self.config.OUTPUT_DIR}/structure_epoch_{epoch}.png", dpi=150, bbox_inches='tight')
        plt.close()

    def save_final_results(self):
        """Save final training results and best structure"""
        print("Saving final results...")

        # Save training history
        history_df = pd.DataFrame({
            'epoch': range(len(self.training_history['g_losses'])),
            'g_loss': self.training_history['g_losses'],
            'd_loss': self.training_history['d_losses'],
            'physics_loss': self.training_history['physics_losses']
        })
        history_df.to_csv(f"{self.config.OUTPUT_DIR}/training_history.csv", index=False)

        # Plot training curves
        plt.figure(figsize=(15, 5))

        plt.subplot(1, 3, 1)
        plt.plot(self.training_history['g_losses'], label='Generator')
        plt.plot(self.training_history['d_losses'], label='Discriminator')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Adversarial Losses')
        plt.legend()
        plt.grid(True)

        plt.subplot(1, 3, 2)
        plt.plot(self.training_history['physics_losses'])
        plt.xlabel('Epoch')
        plt.ylabel('Physics Loss')
        plt.title('Physics-based Loss (R/T matching)')
        plt.grid(True)

        plt.subplot(1, 3, 3)
        plt.semilogy(self.training_history['physics_losses'])
        plt.xlabel('Epoch')
        plt.ylabel('Physics Loss (log scale)')
        plt.title('Physics Loss Convergence')
        plt.grid(True)

        plt.tight_layout()
        plt.savefig(f"{self.config.OUTPUT_DIR}/training_curves.png", dpi=150, bbox_inches='tight')
        plt.close()

        # Save best structure
        if self.training_history['best_structure'] is not None:
            best_structure = self.training_history['best_structure'].detach().cpu().numpy()
            np.save(f"{self.config.OUTPUT_DIR}/best_structure.npy", best_structure)

            # Save detailed analysis of best structure
            self.analyze_best_structure()

            print(f"✓ Best structure saved with loss: {self.training_history['best_loss']:.6f}")
            print(f"✓ Results saved to {self.config.OUTPUT_DIR}/")

    def analyze_best_structure(self):
        """Detailed analysis of the best structure found"""
        if self.training_history['best_structure'] is None:
            return

        structure = self.training_history['best_structure'].detach().cpu().numpy()
        dominant_materials = np.argmax(structure, axis=0)

        print("\n" + "="*60)
        print("BEST STRUCTURE ANALYSIS")
        print("="*60)

        # Material composition
        print("\nMaterial Composition:")
        total_voxels = np.prod(self.config.GRID_SIZE)
        for i, material in enumerate(self.config.MATERIALS):
            count = np.sum(dominant_materials == i)
            percentage = (count / total_voxels) * 100
            print(f"  {material}: {count:,} voxels ({percentage:.1f}%)")

        # Layer analysis (z-direction)
        print(f"\nLayer Analysis (45nm structure, {self.config.GRID_SIZE[2]} layers):")
        layer_thickness = 45.0 / self.config.GRID_SIZE[2]  # nm per layer

        for z in range(0, self.config.GRID_SIZE[2], 10):  # Sample every 10 layers
            layer_materials = dominant_materials[:, :, z]
            dominant_material_idx = np.bincount(layer_materials.flatten()).argmax()
            dominant_material = self.config.MATERIALS[dominant_material_idx]
            z_position = z * layer_thickness
            print(f"  Layer {z:2d} (z={z_position:.1f}nm): {dominant_material}")

        # MEEP validation at multiple wavelengths
        if self.meep_sim:
            print(f"\nMEEP Validation Results:")
            test_wavelengths = [400, 600, 800, 1000, 1200, 1600]
            for wavelength in test_wavelengths:
                R, T = self.validate_with_meep(self.training_history['best_structure'], wavelength)
                A = 1 - R - T  # Absorption
                print(f"  λ={wavelength}nm: R={R:.3f}, T={T:.3f}, A={A:.3f}")

# ====================== Main Function ======================

def main():
    """Main function to run the GAN electromagnetic optimization"""
    print("🚀 GAN for Electromagnetic Simulation with PyMEEP")
    print("="*60)

    # Check PyMEEP availability
    if not MEEP_AVAILABLE:
        print("⚠ WARNING: PyMEEP not available!")
        print("  Install with: conda install -c conda-forge pymeep")
        print("  Continuing with surrogate simulator only...")

    # Initialize configuration
    config = Config()

    # Create GAN trainer
    trainer = GANTrainer(config)

    # Run training
    print(f"\nStarting optimization of {config.TARGET_THICKNESS}nm structure...")
    print(f"Materials: {config.MATERIALS}")
    print(f"Target wavelength range: {config.WAVELENGTH_RANGE[0]}-{config.WAVELENGTH_RANGE[1]}nm")
    print(f"Incident angles: {config.INCIDENT_ANGLES}°")

    best_structure = trainer.train_gan()

    if best_structure is not None:
        print(f"\n🎉 Optimization completed!")
        print(f"Best loss achieved: {trainer.training_history['best_loss']:.6f}")
        print(f"Results saved to: {config.OUTPUT_DIR}/")

        # Final MEEP validation
        if MEEP_AVAILABLE:
            print(f"\nFinal MEEP validation:")
            R, T = trainer.validate_with_meep(best_structure, wavelength_nm=800)
            A = 1 - R - T
            print(f"At 800nm: R={R:.4f}, T={T:.4f}, A={A:.4f}")
    else:
        print("❌ No valid structure found")

if __name__ == "__main__":
    main()
