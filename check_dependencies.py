#!/usr/bin/env python3
"""
Dependency checker for base_prediction_0702.py
This script checks which required packages are installed and provides installation instructions.
"""

import sys
import subprocess

def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✓ {package_name} is installed")
        return True
    except ImportError:
        print(f"✗ {package_name} is NOT installed")
        return False

def main():
    print("Checking dependencies for base_prediction_0702.py...")
    print("=" * 50)
    
    # List of required packages
    packages = [
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("matplotlib", "matplotlib.pyplot"),
        ("scipy", "scipy"),
        ("scikit-learn", "sklearn"),
        ("networkx", "networkx"),
        ("tqdm", "tqdm"),
        ("meep", "meep"),
        ("tmm", "tmm")
    ]
    
    missing_packages = []
    
    for package_name, import_name in packages:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    print("\n" + "=" * 50)
    
    if missing_packages:
        print(f"Missing packages: {', '.join(missing_packages)}")
        print("\nInstallation instructions:")
        print("-" * 30)
        
        # Standard packages
        standard_packages = [pkg for pkg in missing_packages if pkg not in ['meep', 'tmm']]
        if standard_packages:
            print(f"Install standard packages:")
            print(f"pip install {' '.join(standard_packages)}")
        
        # Special packages
        if 'meep' in missing_packages:
            print("\nInstall MEEP (electromagnetic simulation):")
            print("conda install -c conda-forge pymeeus")
            print("# OR")
            print("pip install meep")
            print("# Note: MEEP may require additional system dependencies")
        
        if 'tmm' in missing_packages:
            print("\nInstall TMM (Transfer Matrix Method):")
            print("pip install tmm")
        
        print("\nAlternatively, install all at once:")
        print("pip install numpy pandas matplotlib scipy scikit-learn networkx tqdm tmm")
        print("conda install -c conda-forge pymeeus  # for MEEP")
        
    else:
        print("All dependencies are installed! ✓")
    
    return len(missing_packages) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
