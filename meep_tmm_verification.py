#!/usr/bin/env python3
"""
MEEP vs TMM Verification Program
===============================

This program validates our MEEP implementation against Transfer Matrix Method (TMM)
calculations to ensure our electromagnetic simulation is correct before proceeding
with GAN optimization.

Verification tests:
1. Single layer structures (Al2O3, TiN_4nm)
2. Multi-layer structures 
3. Different wavelengths (400-1600nm)
4. Different incident angles (0°, 15°, 30°, 45°)
5. Both s and p polarizations
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
import os
from scipy import interpolate
import warnings
warnings.filterwarnings('ignore')

# Try to import MEEP
try:
    import meep as mp
    MEEP_AVAILABLE = True
    print("✓ PyMEEP successfully imported")
except ImportError:
    MEEP_AVAILABLE = False
    print("⚠ PyMEEP not available. Please install with: conda install -c conda-forge pymeep")

# Import our material database
from gan_meep_electromagnetic import MaterialDatabase, Config

# ====================== TMM Implementation ======================

class TMM:
    """
    Transfer Matrix Method implementation for validation
    """
    
    def __init__(self):
        pass
    
    def calculate_rt(self, layers, wavelength_um, angle_deg=0, polarization='s'):
        """
        Calculate R,T using Transfer Matrix Method
        
        Args:
            layers: List of dicts with {'d': thickness_um, 'n': n, 'k': k}
            wavelength_um: Wavelength in micrometers
            angle_deg: Incident angle in degrees
            polarization: 's' or 'p'
            
        Returns:
            R, T: Reflectance and transmittance
        """
        # Convert angle to radians
        theta0 = np.radians(angle_deg)
        
        # Vacuum properties
        n0 = 1.0  # Air
        ns = 1.0  # Substrate (air)
        
        # Wave vector
        k0 = 2 * np.pi / wavelength_um
        
        # Calculate angles in each layer using Snell's law
        angles = [theta0]  # Angle in air
        for layer in layers:
            n_complex = complex(layer['n'], layer['k'])
            sin_theta = n0 * np.sin(theta0) / n_complex.real
            if abs(sin_theta) > 1:
                sin_theta = np.sign(sin_theta)  # Total internal reflection
            theta = np.arcsin(sin_theta)
            angles.append(theta)
        
        # Initialize transfer matrix
        M = np.eye(2, dtype=complex)
        
        for i, layer in enumerate(layers):
            n_complex = complex(layer['n'], layer['k'])
            d = layer['d']
            theta = angles[i+1]
            
            # Wave vector component in layer
            kz = k0 * n_complex * np.cos(theta)
            
            # Phase factor
            beta = kz * d
            
            # Fresnel coefficients depend on polarization
            if i == 0:  # First interface (air to layer)
                n1 = n0
                theta1 = theta0
            else:
                n1 = complex(layers[i-1]['n'], layers[i-1]['k'])
                theta1 = angles[i]
            
            n2 = n_complex
            theta2 = theta
            
            if polarization == 's':
                # s-polarization (TE)
                r = (n1*np.cos(theta1) - n2*np.cos(theta2)) / (n1*np.cos(theta1) + n2*np.cos(theta2))
                t = 2*n1*np.cos(theta1) / (n1*np.cos(theta1) + n2*np.cos(theta2))
            else:
                # p-polarization (TM)
                r = (n2*np.cos(theta1) - n1*np.cos(theta2)) / (n2*np.cos(theta1) + n1*np.cos(theta2))
                t = 2*n1*np.cos(theta1) / (n2*np.cos(theta1) + n1*np.cos(theta2))
            
            # Interface matrix
            if i == 0:
                Mi = np.array([[1, r], [r, 1]]) / t
            else:
                Mi = np.eye(2, dtype=complex)
            
            # Propagation matrix
            Mp = np.array([[np.exp(-1j*beta), 0], [0, np.exp(1j*beta)]])
            
            # Combine matrices
            M = M @ Mi @ Mp
        
        # Final interface (last layer to substrate)
        n1 = complex(layers[-1]['n'], layers[-1]['k'])
        theta1 = angles[-1]
        n2 = ns
        theta2 = np.arcsin(n1.real * np.sin(theta1) / n2) if abs(n1.real * np.sin(theta1) / n2) <= 1 else np.pi/2
        
        if polarization == 's':
            r = (n1*np.cos(theta1) - n2*np.cos(theta2)) / (n1*np.cos(theta1) + n2*np.cos(theta2))
            t = 2*n1*np.cos(theta1) / (n1*np.cos(theta1) + n2*np.cos(theta2))
        else:
            r = (n2*np.cos(theta1) - n1*np.cos(theta2)) / (n2*np.cos(theta1) + n1*np.cos(theta2))
            t = 2*n1*np.cos(theta1) / (n2*np.cos(theta1) + n1*np.cos(theta2))
        
        Mf = np.array([[1, r], [r, 1]]) / t
        M = M @ Mf
        
        # Calculate reflection and transmission coefficients
        r_total = M[1, 0] / M[0, 0]
        t_total = 1.0 / M[0, 0]
        
        # Calculate reflectance and transmittance
        R = abs(r_total)**2
        T = abs(t_total)**2 * (ns * np.cos(theta2).real) / (n0 * np.cos(theta0))
        
        return float(R.real), float(T.real)

# ====================== MEEP Simulator for Verification ======================

class MEEPVerificationSimulator:
    """
    Simplified MEEP simulator for verification against TMM
    """
    
    def __init__(self, material_db):
        self.material_db = material_db
        
        if not MEEP_AVAILABLE:
            raise RuntimeError("PyMEEP is required for verification")
    
    def simulate_layers(self, layers, wavelength_um, angle_deg=0, polarization='s'):
        """
        Simulate layered structure using MEEP
        
        Args:
            layers: List of dicts with {'d': thickness_um, 'n': n, 'k': k, 'name': material_name}
            wavelength_um: Wavelength in micrometers
            angle_deg: Incident angle in degrees
            polarization: 's' or 'p'
            
        Returns:
            R, T: Reflectance and transmittance
        """
        frequency = 1.0 / wavelength_um
        
        # Calculate total structure thickness
        total_thickness = sum(layer['d'] for layer in layers)
        
        # Define simulation cell
        pml_thickness = 1.0  # μm
        padding = 2.0  # μm
        cell_z = total_thickness + 2*pml_thickness + 2*padding
        cell_x = 4.0  # μm (wide enough to avoid edge effects)
        cell_y = 4.0  # μm
        
        cell = mp.Vector3(cell_x, cell_y, cell_z)
        
        # Define PML layers
        pml_layers = [mp.PML(thickness=pml_thickness)]
        
        # Create geometry
        geometry = []
        z_pos = -total_thickness/2
        
        for layer in layers:
            # Get material medium
            n_complex = complex(layer['n'], layer['k'])
            epsilon = n_complex**2
            
            # Create medium
            if abs(epsilon.imag) > 1e-10:
                # Lossy material
                medium = mp.Medium(epsilon=epsilon.real, 
                                 D_conductivity=2*epsilon.imag*frequency/(2*np.pi))
            else:
                # Lossless material
                medium = mp.Medium(epsilon=epsilon.real)
            
            # Create block
            block = mp.Block(size=mp.Vector3(cell_x, cell_y, layer['d']),
                           center=mp.Vector3(0, 0, z_pos + layer['d']/2),
                           material=medium)
            geometry.append(block)
            
            z_pos += layer['d']
        
        # Define source
        source_z = -cell_z/2 + pml_thickness + padding/2
        
        # Handle polarization and angle
        if angle_deg == 0:
            # Normal incidence
            if polarization == 's':
                component = mp.Ex
            else:
                component = mp.Ey
            
            sources = [mp.Source(mp.GaussianSource(frequency, fwidth=frequency*0.1),
                               component=component,
                               center=mp.Vector3(0, 0, source_z))]
        else:
            # Angled incidence (simplified - use plane wave)
            k_vector = mp.Vector3(0, 0, 1).rotate(mp.Vector3(1, 0, 0), np.radians(angle_deg))
            
            if polarization == 's':
                component = mp.Ex
            else:
                component = mp.Ey
                
            sources = [mp.Source(mp.GaussianSource(frequency, fwidth=frequency*0.1),
                               component=component,
                               center=mp.Vector3(0, 0, source_z))]
        
        # Create simulation
        sim = mp.Simulation(cell_size=cell,
                          boundary_layers=pml_layers,
                          geometry=geometry,
                          sources=sources,
                          resolution=20)
        
        # Add flux monitors
        refl_z = source_z + padding/4
        tran_z = cell_z/2 - pml_thickness - padding/2
        
        monitor_size = mp.Vector3(cell_x*0.8, cell_y*0.8, 0)
        
        refl = sim.add_flux(frequency, 0, 1,
                          mp.FluxRegion(center=mp.Vector3(0, 0, refl_z),
                                      size=monitor_size))
        
        tran = sim.add_flux(frequency, 0, 1,
                          mp.FluxRegion(center=mp.Vector3(0, 0, tran_z),
                                      size=monitor_size))
        
        # Run simulation
        sim.run(until_after_sources=mp.stop_when_fields_decayed(50, component, 
                                                               mp.Vector3(0, 0, tran_z), 1e-6))
        
        # Get fluxes
        refl_flux = mp.get_fluxes(refl)[0]
        tran_flux = mp.get_fluxes(tran)[0]
        
        # Run reference simulation for normalization
        sim.reset_meep()
        
        # Remove geometry for reference
        sim = mp.Simulation(cell_size=cell,
                          boundary_layers=pml_layers,
                          geometry=[],  # No geometry
                          sources=sources,
                          resolution=20)
        
        refl_ref = sim.add_flux(frequency, 0, 1,
                              mp.FluxRegion(center=mp.Vector3(0, 0, refl_z),
                                          size=monitor_size))
        
        sim.run(until_after_sources=mp.stop_when_fields_decayed(50, component,
                                                               mp.Vector3(0, 0, refl_z), 1e-6))
        
        incident_flux = mp.get_fluxes(refl_ref)[0]
        
        # Calculate R and T
        if incident_flux != 0:
            R = -refl_flux / incident_flux  # Negative because reflection is in opposite direction
            T = tran_flux / incident_flux
        else:
            R, T = 0.0, 0.0
        
        return max(0, R), max(0, T)

# ====================== Verification Program ======================

class MEEPTMMVerification:
    """
    Main verification program to compare MEEP vs TMM results
    """

    def __init__(self):
        self.config = Config()
        self.material_db = MaterialDatabase(self.config)
        self.tmm = TMM()

        if MEEP_AVAILABLE:
            self.meep_sim = MEEPVerificationSimulator(self.material_db)
            print("✓ MEEP verification simulator initialized")
        else:
            self.meep_sim = None
            print("⚠ MEEP not available - verification will be limited")

        self.results = []

    def create_test_layers(self, test_case):
        """Create layer structure for testing"""
        layers = []

        if test_case == "Al2O3_single":
            # Single Al2O3 layer
            n, k = self.material_db.get_nk('Al2O3', 0.8)  # 800nm
            layers = [{'d': 0.045, 'n': n, 'k': k, 'name': 'Al2O3'}]  # 45nm

        elif test_case == "TiN_single":
            # Single TiN layer
            n, k = self.material_db.get_nk('TiN_4nm', 0.8)  # 800nm
            layers = [{'d': 0.045, 'n': n, 'k': k, 'name': 'TiN_4nm'}]  # 45nm

        elif test_case == "TiO2_single":
            # Single TiO2 layer
            n, k = self.material_db.get_nk('TiO2', 0.8)  # 800nm
            layers = [{'d': 0.045, 'n': n, 'k': k, 'name': 'TiO2'}]  # 45nm

        elif test_case == "bilayer":
            # Bilayer: TiO2 + Al2O3
            n1, k1 = self.material_db.get_nk('TiO2', 0.8)
            n2, k2 = self.material_db.get_nk('Al2O3', 0.8)
            layers = [
                {'d': 0.0225, 'n': n1, 'k': k1, 'name': 'TiO2'},    # 22.5nm
                {'d': 0.0225, 'n': n2, 'k': k2, 'name': 'Al2O3'}   # 22.5nm
            ]

        elif test_case == "multilayer":
            # Four-layer structure
            n1, k1 = self.material_db.get_nk('TiN_4nm', 0.8)
            n2, k2 = self.material_db.get_nk('TiO2', 0.8)
            n3, k3 = self.material_db.get_nk('Al2O3', 0.8)
            n4, k4 = self.material_db.get_nk('TiN_30nm', 0.8)
            layers = [
                {'d': 0.01125, 'n': n1, 'k': k1, 'name': 'TiN_4nm'},   # 11.25nm
                {'d': 0.01125, 'n': n2, 'k': k2, 'name': 'TiO2'},      # 11.25nm
                {'d': 0.01125, 'n': n3, 'k': k3, 'name': 'Al2O3'},     # 11.25nm
                {'d': 0.01125, 'n': n4, 'k': k4, 'name': 'TiN_30nm'}   # 11.25nm
            ]

        return layers

    def run_single_verification(self, test_case, wavelength_nm, angle_deg=0, polarization='s'):
        """Run verification for a single test case"""
        wavelength_um = wavelength_nm * 1e-3

        print(f"\n--- Testing {test_case} at {wavelength_nm}nm, {angle_deg}°, {polarization}-pol ---")

        # Create test layers
        layers = self.create_test_layers(test_case)

        if not layers:
            print(f"❌ Could not create layers for {test_case}")
            return None

        # Print layer information
        print("Layer structure:")
        for i, layer in enumerate(layers):
            print(f"  Layer {i+1}: {layer['name']} - {layer['d']*1000:.1f}nm, n={layer['n']:.3f}, k={layer['k']:.3f}")

        # Calculate TMM result
        try:
            start_time = time.time()
            R_tmm, T_tmm = self.tmm.calculate_rt(layers, wavelength_um, angle_deg, polarization)
            tmm_time = time.time() - start_time
            print(f"TMM: R={R_tmm:.6f}, T={T_tmm:.6f}, A={1-R_tmm-T_tmm:.6f} (time: {tmm_time:.3f}s)")
        except Exception as e:
            print(f"❌ TMM calculation failed: {e}")
            return None

        # Calculate MEEP result
        if self.meep_sim:
            try:
                start_time = time.time()
                R_meep, T_meep = self.meep_sim.simulate_layers(layers, wavelength_um, angle_deg, polarization)
                meep_time = time.time() - start_time
                print(f"MEEP: R={R_meep:.6f}, T={T_meep:.6f}, A={1-R_meep-T_meep:.6f} (time: {meep_time:.3f}s)")
            except Exception as e:
                print(f"❌ MEEP calculation failed: {e}")
                R_meep, T_meep = 0.0, 0.0
        else:
            R_meep, T_meep = 0.0, 0.0
            print("⚠ MEEP not available")

        # Calculate differences
        if self.meep_sim:
            diff_R = abs(R_meep - R_tmm)
            diff_T = abs(T_meep - T_tmm)
            rel_diff_R = diff_R / max(R_tmm, 1e-10) * 100
            rel_diff_T = diff_T / max(T_tmm, 1e-10) * 100

            print(f"Differences: ΔR={diff_R:.6f} ({rel_diff_R:.2f}%), ΔT={diff_T:.6f} ({rel_diff_T:.2f}%)")

            # Check if verification passes
            tolerance = 5.0  # 5% tolerance
            passes = (rel_diff_R < tolerance) and (rel_diff_T < tolerance)
            status = "✅ PASS" if passes else "❌ FAIL"
            print(f"Verification: {status}")
        else:
            diff_R = diff_T = rel_diff_R = rel_diff_T = 0.0
            passes = False
            status = "⚠ SKIP"

        # Store results
        result = {
            'test_case': test_case,
            'wavelength_nm': wavelength_nm,
            'angle_deg': angle_deg,
            'polarization': polarization,
            'R_tmm': R_tmm,
            'T_tmm': T_tmm,
            'R_meep': R_meep,
            'T_meep': T_meep,
            'diff_R': diff_R,
            'diff_T': diff_T,
            'rel_diff_R': rel_diff_R,
            'rel_diff_T': rel_diff_T,
            'passes': passes,
            'status': status
        }

        self.results.append(result)
        return result

    def run_comprehensive_verification(self):
        """Run comprehensive verification across multiple test cases"""
        print("🔬 MEEP vs TMM Comprehensive Verification")
        print("="*60)

        # Test cases
        test_cases = [
            "Al2O3_single",
            "TiO2_single",
            "bilayer",
            "multilayer"
        ]

        # Test wavelengths (nm)
        wavelengths = [400, 600, 800, 1000, 1200, 1600]

        # Test angles (degrees)
        angles = [0, 15, 30]

        # Test polarizations
        polarizations = ['s']  # Start with s-polarization

        total_tests = len(test_cases) * len(wavelengths) * len(angles) * len(polarizations)
        current_test = 0

        print(f"Running {total_tests} verification tests...")

        for test_case in test_cases:
            for wavelength in wavelengths:
                for angle in angles:
                    for polarization in polarizations:
                        current_test += 1
                        print(f"\n[{current_test}/{total_tests}] ", end="")

                        result = self.run_single_verification(test_case, wavelength, angle, polarization)

                        if result is None:
                            continue

        # Analyze results
        self.analyze_verification_results()

    def analyze_verification_results(self):
        """Analyze and summarize verification results"""
        if not self.results:
            print("\n❌ No verification results to analyze")
            return

        print("\n" + "="*60)
        print("VERIFICATION RESULTS SUMMARY")
        print("="*60)

        # Convert to DataFrame for analysis
        df = pd.DataFrame(self.results)

        # Overall statistics
        total_tests = len(df)
        passed_tests = len(df[df['passes'] == True])
        failed_tests = len(df[df['passes'] == False])
        skipped_tests = len(df[df['status'] == '⚠ SKIP'])

        print(f"\nOverall Results:")
        print(f"  Total tests: {total_tests}")
        print(f"  Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"  Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"  Skipped: {skipped_tests} ({skipped_tests/total_tests*100:.1f}%)")

        if passed_tests > 0:
            # Statistics for passed tests
            passed_df = df[df['passes'] == True]
            print(f"\nAccuracy Statistics (Passed Tests):")
            print(f"  Mean R difference: {passed_df['rel_diff_R'].mean():.3f}%")
            print(f"  Mean T difference: {passed_df['rel_diff_T'].mean():.3f}%")
            print(f"  Max R difference: {passed_df['rel_diff_R'].max():.3f}%")
            print(f"  Max T difference: {passed_df['rel_diff_T'].max():.3f}%")

        if failed_tests > 0:
            print(f"\nFailed Tests:")
            failed_df = df[df['passes'] == False]
            for _, row in failed_df.iterrows():
                print(f"  {row['test_case']} @ {row['wavelength_nm']}nm, {row['angle_deg']}°: "
                      f"ΔR={row['rel_diff_R']:.1f}%, ΔT={row['rel_diff_T']:.1f}%")

        # Save results
        os.makedirs("verification_results", exist_ok=True)
        df.to_csv("verification_results/meep_tmm_comparison.csv", index=False)
        print(f"\n✓ Detailed results saved to verification_results/meep_tmm_comparison.csv")

        # Create visualization
        self.create_verification_plots(df)

        # Final verdict
        if passed_tests == total_tests - skipped_tests:
            print(f"\n🎉 VERIFICATION PASSED!")
            print(f"   MEEP implementation is validated against TMM")
            print(f"   Ready to proceed with GAN optimization")
            return True
        else:
            print(f"\n❌ VERIFICATION FAILED!")
            print(f"   {failed_tests} tests failed - MEEP implementation needs fixing")
            print(f"   Do not proceed with GAN optimization until issues are resolved")
            return False

    def create_verification_plots(self, df):
        """Create visualization plots for verification results"""
        if df.empty:
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # Plot 1: R comparison
        axes[0, 0].scatter(df['R_tmm'], df['R_meep'], alpha=0.7, c=df['wavelength_nm'], cmap='viridis')
        axes[0, 0].plot([0, 1], [0, 1], 'r--', label='Perfect agreement')
        axes[0, 0].set_xlabel('TMM Reflectance')
        axes[0, 0].set_ylabel('MEEP Reflectance')
        axes[0, 0].set_title('Reflectance Comparison')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: T comparison
        axes[0, 1].scatter(df['T_tmm'], df['T_meep'], alpha=0.7, c=df['wavelength_nm'], cmap='viridis')
        axes[0, 1].plot([0, 1], [0, 1], 'r--', label='Perfect agreement')
        axes[0, 1].set_xlabel('TMM Transmittance')
        axes[0, 1].set_ylabel('MEEP Transmittance')
        axes[0, 1].set_title('Transmittance Comparison')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Relative differences vs wavelength
        axes[1, 0].plot(df['wavelength_nm'], df['rel_diff_R'], 'o-', label='R difference', alpha=0.7)
        axes[1, 0].plot(df['wavelength_nm'], df['rel_diff_T'], 's-', label='T difference', alpha=0.7)
        axes[1, 0].axhline(y=5, color='r', linestyle='--', label='5% tolerance')
        axes[1, 0].set_xlabel('Wavelength (nm)')
        axes[1, 0].set_ylabel('Relative Difference (%)')
        axes[1, 0].set_title('Accuracy vs Wavelength')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Test case summary
        test_case_summary = df.groupby('test_case')['passes'].agg(['count', 'sum']).reset_index()
        test_case_summary['pass_rate'] = test_case_summary['sum'] / test_case_summary['count'] * 100

        bars = axes[1, 1].bar(range(len(test_case_summary)), test_case_summary['pass_rate'])
        axes[1, 1].set_xlabel('Test Case')
        axes[1, 1].set_ylabel('Pass Rate (%)')
        axes[1, 1].set_title('Pass Rate by Test Case')
        axes[1, 1].set_xticks(range(len(test_case_summary)))
        axes[1, 1].set_xticklabels(test_case_summary['test_case'], rotation=45)
        axes[1, 1].grid(True, alpha=0.3)

        # Color bars based on pass rate
        for i, bar in enumerate(bars):
            if test_case_summary.iloc[i]['pass_rate'] == 100:
                bar.set_color('green')
            elif test_case_summary.iloc[i]['pass_rate'] >= 80:
                bar.set_color('orange')
            else:
                bar.set_color('red')

        plt.tight_layout()
        plt.savefig("verification_results/meep_tmm_verification.png", dpi=150, bbox_inches='tight')
        plt.close()

        print(f"✓ Verification plots saved to verification_results/meep_tmm_verification.png")

def main():
    """Main verification program"""
    print("🔬 MEEP vs TMM Verification Program")
    print("="*50)

    if not MEEP_AVAILABLE:
        print("❌ PyMEEP not available!")
        print("   Please install with: conda install -c conda-forge pymeep")
        print("   Cannot proceed with verification.")
        return False

    # Create verification instance
    verifier = MEEPTMMVerification()

    # Run comprehensive verification
    verification_passed = verifier.run_comprehensive_verification()

    return verification_passed

if __name__ == "__main__":
    verification_passed = main()

    if verification_passed:
        print("\n🚀 Proceeding to GAN optimization...")
        # Here we would call the GAN optimization
        # from gan_meep_electromagnetic import main as gan_main
        # gan_main()
    else:
        print("\n⚠ Fix MEEP implementation before proceeding with GAN optimization")
