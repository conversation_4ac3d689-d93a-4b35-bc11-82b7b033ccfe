import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import meep as mp
from scipy import interpolate
from tqdm import tqdm
import networkx as nx
from collections import defaultdict
from sklearn.neural_network import MLPRegressor
import os
import time
import tmm  # TMM���

# ====================== Mn�p ======================
class Config:
    # ���Mn
    MATERIAL_NK_FILES = {
        'TiN_4nm': 'data/TiN-4nm.csv',     # 4nm TiN�n,kpn
        'TiO2': 'data/TiO2.csv',           # TiO2�n,kpn
        'TiN_30nm': 'data/TiN-30nm.csv',   # 30nm TiN�n,kpn
        'Al2O3': 'data/Al2O3.txt'         # Al2O3�n,kpn
    }
    
    EXPERIMENTAL_DATA = {
        'TiN_4nm': 'data/data.txt',       # 4nm TiN���R,Tpn
        'Al2O3': 'data/Al2O3.txt'        # Al2O3���R,Tpn
    }
    
    # !��p
    WAVELENGTHS = np.linspace(400, 800, 10)  # ��(nm)
    ANGLES = [0, 30, 45, 60]                # eҦ(�)
    POLARIZATIONS = ['s', 'p']               # O/�
    
    # P��p
    GRID_SIZE = (100, 50, 100)  # (x, y, z) Q<'
    MATERIALS = ['TiN_4nm', 'TiO2', 'TiN_30nm', 'Al2O3']  # ��P�
    TARGET_THICKNESS = 45  # �����(nm)
    
    # FDTD�p
    RESOLUTION = 30  # FDTDQ<��(points/�m)
    PML_THICKNESS = 0.3  # PML��(�m) 300nm
    SIMULATION_TIME = 100  # !���(fs)
    
    # ���p
    VALIDATION_WAVELENGTHS = np.linspace(400, 800, 5)  # ��(��
    VALIDATION_ANGLE = 0  # ��(eҦ
    VALIDATION_POLARIZATION = 's'  # ��(O/
    
    # ���n
    OUTPUT_DIR = 'results/'
    SAVE_INTERVAL = 5  # �!���X !Ӝ

# nݓ��UX(
os.makedirs(Config.OUTPUT_DIR, exist_ok=True)

# ====================== P�pn� ======================
class MaterialDatabase:
    """
    P�If8ppn�
    ���}��<��P��n,k<ʞ�KτR,Tpn
    
    ^':
        materials_nk: X���P��n,k�<�p
        materials_RT: X���KτR,Tpn
        
    ��:
        load_material_data: �}P��If8ppn
        load_experimental_RT: �}��KτR,Tpn
        get_nk: �����n,k<
        get_epsilon: ��
�58p
        get_medium: �Meep�(�a
        get_experimental_RT: �֞�R,Tpn
    """
    
    def __init__(self, config):
        self.config = config
        self.materials_nk = {}
        self.materials_RT = {}
        self.wavelength_unit = 1e-3  # UM:�s
        
        # �}@	P�pn
        for material in config.MATERIALS:
            if material in config.MATERIAL_NK_FILES:
                self.load_material_data(material, config.MATERIAL_NK_FILES[material])
                
        # �}��pn
        for material, path in config.EXPERIMENTAL_DATA.items():
            self.load_experimental_RT(material, path)
    
    def load_material_data(self, name, file_path):
        """
        �}P��If8ppn(n,k)
        
        �p:
            name: P�
�
            file_path: pn���
            
        A:
            1. 9n��iU
n�<(csvtxt)
            2. �������n,k<
            3. ��<�p
        """
        if file_path.endswith('.csv'):
            data = pd.read_csv(file_path)
        else:  # txt<
            data = pd.read_csv(file_path, sep='\t', header=None, 
                              names=['wavelength', 'n', 'k'])
        
        wavelengths = data['wavelength'].values * self.wavelength_unit
        n_values = data['n'].values
        k_values = data['k'].values
        
        # ��<�p
        self.materials_nk[name] = {
            'n': interpolate.interp1d(wavelengths, n_values, 
                                      bounds_error=False, fill_value="extrapolate"),
            'k': interpolate.interp1d(wavelengths, k_values, 
                                      bounds_error=False, fill_value="extrapolate")
        }
    
    def load_experimental_RT(self, name, file_path):
        """
        �}��KτR,Tpn
        
        �p:
            name: P�
�
            file_path: pn���
        """
        if file_path.endswith('.txt'):
            data = pd.read_csv(file_path, sep='\t', header=None, 
                              names=['wavelength', 'R', 'T'])
        else:
            data = pd.read_csv(file_path)
            
        self.materials_RT[name] = data
        
    def get_nk(self, material_name, wavelength):
        """
        �����n,k<
        
        �p:
            material_name: P�
�
            wavelength: �(�s)
            
        ��:
            (n,� k): ����I�p
            
        :�:
            material_db.get_nk('TiN_4nm', 0.5) -> (1.8, 0.2)
        """
        if material_name not in self.materials_nk:
            raise ValueError(f"Material {material_name} nk data not found")
        
        n = self.materials_nk[material_name]['n'](wavelength)
        k = self.materials_nk[material_name]['k'](wavelength)
        return n, k
    
    def get_epsilon(self, material_name, wavelength):
        """
        �����
�58p
        
        �p:
            material_name: P�
�
            wavelength: �(�s)
            
        ��:
            complex: 
�58p
            
        :�:
            material_db.get_epsilon('TiN_4nm', 0.5) -> (1.8+0.2j)**2
        """
        n, k = self.get_nk(material_name, wavelength)
        epsilon = (n + 1j * k) ** 2
        return epsilon
    
    def get_medium(self, material_name, wavelength):
        """�Meep�(�a"""
        return mp.Medium(epsilon=self.get_epsilon(material_name, wavelength))
    
    def get_experimental_RT(self, material_name, wavelength):
        """
        �֞�R,Tpn
        
        �p:
            material_name: P�
�
            wavelength: �(nm)
            
        ��:
            (R, T): ����
        """
        if material_name not in self.materials_RT:
            return None, None
            
        data = self.materials�.RT[material_name]
        # ~0 �ф�
        idx = np.abs(data['wavelength'] - wavelength).argmin()
        return data.iloc[idx]['R'], data.iloc[idx]['T']
    
    def get_interpolated_RT(self, material_name, wavelength):
        """
        ���<���R,Tpn
        
        �p:
            material_name: P�
�
            wavelength: �(nm)
            
        ��:
            (R, T): �<�����
        """
        if material_name not in self.materials_RT:
            return None, None
            
        data = self.materials_RT[material_name]
        interpolator_R = interpolate.interp1d(data['wavelength'], data['R'], 
                                              bounds_error=False, fill_value="extrapolate")
        interpolator_T = interpolate.interp1d(data['wavelength'], data['T'], 
                                              bounds_error=False, fill_value="extrapolate")
        return interpolator_R(wavelength), interpolator_T(wavelength)

# ====================== P� ======================
class MaterialDistribution:
    """
    P��
    �����	�P�ӄ
    
    ^':
        grid: 	�Q<X��*UC�(P�{�, @^:�)
        material_graph: v��pnӄ
        region_materials: :�0P�� 
        material_regions: P�0:߄ 
        
    ��:
        initialize_distribution: ��:P�
        assign_material: MP�0�Mn
        get_connected_components: (v��ޥ���P�
        optimize_distribution: P�
        visualize_slice: �� *G
    """
    
    def __init__(self, config):
        self.config = config
        self.grid = np.empty(config.GRID_SIZE, dtype=object)
        self.material_graph = nx.Graph()
        self.region_materials = {}  # :�ID -> P�{�
        self.material_regions = defaultdict(list)  # P�{� -> :�IDh
        self.initialize_distribution()
    
    def initialize_distribution(self):
        """
        ��:P�
        
        A:
            1. :�*Q<UC�:MP�
            2. �v��ӄ
            3. ޥ����P�UC
        """
        # �:�P�
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    mat_idx = np.random.randint(len(self.config.MATERIALS))
                    material = self.config.MATERIALS[mat_idx]
                    self.grid[i, j, k] = (material, None)
                    self.material_graph.add_node((i, j, k))
        
        # ޥ����P�UC
        self.get_connected_components()
        
        print(f"��: {self.grid.shape} Q<'")
    
    def get_connected_components(self):
        """
        (v��ޥ���P�UC
        
        A:
            1. M�@	Q<UC
            2. ��6*���EUC
            3. ��P��ޥ��
        """
        # �H�n@	:�
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    self.grid[i, j, k] = (self.grid[i, j, k][0], None)
        
        # �n�ӄ
        self.material_graph = nx.Graph()
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    self.material_graph.add_node((i, j, k))
        
        # ���P���UC��
        directions = [
            (1, 0, 0), (-1, 0, 0),
            (0, 1, 0), (0, -1, 0),
            (0, 0, 1), (0, 0, -1)
        ]
        
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    current_mat = self.grid[i, j, k][0]
                    
                    for dx, dy, dz in directions:
                        ni, nj, nk = i+dx, j+dy, k+dz
                        if (0 <= ni < self.config.G�ID_SIZE[0] and 
                            0 <= nj < self.config.GRID_SIZE[1] and 
                            0 <= nk < self.config.GRID_SIZE[2]):
                            
                            neighbor_mat = self.grid[ni, nj, nk][0]
                            if current_mat == neighbor_mat:
                                self.material_graph.add_edge((i, j, k), (ni, nj, nk))
        
        # �+���:�	
        regions = list(nx.connected_components(self.material_graph))
        
        # ��Q<�:� 
        for region_id, component in enumerate(regions):
            material = self.grid[next(iter(component))][0]  # ��:߄P�{�
            
            # ��:� 
            self.region_materials[region_id] = material
            if material not in self.material_regions:
                self.material_regions[material] = []
            self.material_regions[material].append(region_id)
            
            # ��Q<UC
            for pos in component:
                i, j, k = pos
                self.grid[i, j, k] = (material, region_id)
    
    def assign_material(self, position, material):
        """
        MP�0�Mn
        
        �p:
            position: (i, j, k) 	�P
            material: P�
�
        """
        i, j, k = position
        self.grid[i, j, k] = (material, None)
        self.get_connected_components()  # Ͱ��:�
    
    def optimize_distribution(self, loss_matrix, learning_rate=0.1, mutation_rate=0.1):
        """
        P�
        
        �p:
            loss_matrix: �*:߄_1<�5
            learning_rate: f`�
            mutation_rate: ��
            
        A:
            1. ��_1�5���*P����
            2. ���\�:	�:��LP��b
            3. ��\� ����:9�:�P�
        """
        # ����P��sG_1
        material_loss = {}
        for material in self.config.MATERIALS:
            if material in self.material_regions:
                regions = self.material_regions[material]
                total_loss = sum(loss_matrix[region] for region in regions)
                material_loss[material] = total_loss / len(regions)
            else:
                material_loss[material] = float('inf')  # *���P�_1�:�w'
        
        # 	_1��P�
        sorted_materials = sorted(self.config.MATERIALS, key=lambda m: material_loss[m])
        
        # ���\�_1:��b:N_1P�
        for material, regions in list(self.material_regions.items()):
            if material == sorted_materials[-1]:  #  �_1P�
                new_material = sorted_materials[0]  #  N_1P�
                for region in regions:
                    # ~0�:߄@	UCv��P�
                    for i in range(self.config.GRID_SIZE[0]):
                        for j in range(self.config.GRID_SIZE[1]):
                            for k in range(self.config.GRID_SIZE[2]):
                                if self.grid[i, j, k][1] == region:
                                    self.grid[i, j, k] = (new_material, region)
                self.region_materials[region] = new_material
                
        # ��\�:9� �:�
        for region in list(self.region_materials.keys()):
            if np.random.rand() < mutation_rate:
                current_material = self.region_materials[region]
                # �:	�P�
�SM	
                new_material = np.random.choice([m for m in self.config.MATERIALS if m != current_material])
                
                # ���:߄@	UC
                for i in range(self.config.GRID_SIZE[0]):
                    for j in range(self.config.GRID_SIZE[1]):
                        for k in range(self.config.GRID_SIZE[2]):
                            if self.grid[i, j, k][1] == region:
                                self.grid[i, j, k] = (new_material, region)
                self.region_materials[region] = new_material
        
        # ��:� 
        self.get_connected_components()
    
    def visualize_slice(self, slice_idx, axis='z', save_path=None):
        """
        �� *G
        
        �p:
            slice_idx: G"
            axis: Gt ('x', 'y', 'z')
            save_path: ���X�
        """
        material_to_idx = {mat: idx for idx, mat in enumerate(self.config.MATERIALS)}
        
        if axis == 'x':
            slice_data = np.zeros((self.config.GRID_SIZE[1], self.config.GRID_SIZE[2]))
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    slice_data[j, k] = material_to_idx[self.grid[slice_idx, j, k][0]]
        elif axis == 'y':
            slice_data = np.zeros((self.config.GRID_SIZE[0], self.config.GRID_SIZE[2]))
            for i in range(self.config.GRID_SIZE[0]):
                for k in range(self.config.GRID_SIZE[2]):
                    slice_data[i, k] = material_to_idx[self.grid[i, slice_idx, k][0]]
        else:  # zt
            slice_data = np.zeros((self.config.GRID_SIZE[0], self.config.GRID_SIZE[1]))
            for i in range(self.config.GRID_SIZE[0]):
                for j in range(self.config.GRID_SIZE[1]):
                    slice_data[i, j] = material_to_idx[self.grid[i, j, slice_idx][0]]
        
        plt.figure(figsize=(10, 8))
        plt.imshow(slice_data, cmap='viridis')
        plt.colorbar(ticks=range(len(self.config.MATERIALS)), 
                    label='Materials')
        plt.clim(0, len(self.config.MATERIALS) - 1)
        plt.title(f'Material Distribution along {axis}-axis at index {slice_idx}')
        plt.xlabel('Y' if axis == 'x' else 'X')
        plt.ylabel('X' if axis == 'y' else 'Y')
        
        if save_path:
            plt.savefig(save_path, dpi=150)
            plt.close()
        else:
            plt.show()

# ====================== Meep FDTD !�h ======================
class MeepSimulator:
    """
    ��Meep�FDTD!�h
    ���Bӄv��R,T
    
    ^':
        material_db: P�pn���
        config: Mn�a
        
    ��:
        create_layered_structure: �B�(ӄ
        simulate: gLFDTD!�
        simulate_validation_structure: gL��ӄ!�
    """
    
    def __init__(self, material_db, config):
        self.material_db = material_db
        self.config = config
    
    def create_layered_structure(self, material_dist, wavelength):
        """
        �P��Bӄ
        
        �p:
            material_dist: MaterialDistribution��
            wavelength: SM!��(�s)
            
        ��:
            geometry: Meep�Uӄ
            layer_thickness: B��h
            materials: BP�h
        """
        # ��B��
        unit_thickness = self.config.TARGET_THICKNESS / self.config.GRID_SIZE[2] * 1e-3  # lb:�m
        layer_thickness = [unit_thickness] * self.config.GRID_SIZE[2]
        
        # zB
        air = mp.Medium(epsilon=1.0)
        
        # ݝ�l� (Al2O3)
        substrate_thickness = 0.430  # ݝ�l���(�m)
        substrate = self.material_db.get_medium('Al2O3', wavelength)
        
        # ��Uӄh
        geometry = []
        current_height = 0
        z_positions = []
        
        # ��P�B
        for k in range(self.config.GRID_SIZE[2]):
            # ��SMzMn
            z_center = current_height + layer_thickness[k] / 2
            z_positions.append(z_center)
            current_height += layer_thickness[k]
            
            # ��P�(��z=ksb
�;�P�)
            mat_counts = defaultdict(int)
            for i in range(self.config.GRID_SIZE[0]):
                for j in range(self.config.GRID_SIZE[1]):
                    material = material_dist.grid[i, j, k][�]
                    mat_counts[material] += 1
                    
            # ��� �A�P�
            layer_material = max(mat_counts, key=mat_counts.get)
            medium = self.material_db.get_medium(layer_material, wavelength)
            
            # ��P�B
            geometry.append(mp.Block(
                size=mp.Vector3(mp.inf, mp.inf, layer_thickness[k]),
                center=mp.Vector3(0, 0, z_center),
                material=medium
            ))
        
        # ��l�B
        current_height += layer_thickness[-1] / 2
        z_center_sub = current_height + substrate_thickness / 2
        geometry.append(mp.Block(
            size=mp.Vector3(mp.inf, mp.inf, substrate_thickness),
            center=mp.Vector3(0, 0, z_center_sub),
            material=substrate
        ))
        
        # ���UӄB���P�{�
        return geometry, layer_thickness, [mat for mat in mat_counts.keys()]
    
    def simulate(self, material_dist, wavelength, polarization, angle):
        """
        gLFDTD!ߡ�
        
        �p:
            material_dist: MaterialDistribution��
            wavelength: �(nm)
            polarization: O/ ('s''p')
            angle: eҦ(�)
            
        ��:
            R, T: ����
        """
        wavelength_um = wavelength * 1e-3  # lb:�s
        try:
            # 1. ��Uӄ
            geometry, layer_thickness, _ = self.create_layered_structure(material_dist, wavelength_um)
            total_height = sum(layer_thickness) + 0.430  # ��+l�ئ(�m)
            
            # 2. �n��:�
            cell_size_z = total_height + 1.0  # �z�
            cell_size = mp.Vector3(0, 0, cell_size_z)  # 1D!�
            
            # 3. �nI�
            fcen = 1 / wavelength_um  # -Ñ�
            src_pt = mp.Vector3(0, 0, -0.45 * cell_size_z)  # �Mn
            
            if polarization == 's':
                src_cmpt = mp.Ex
            else:  # pO/
                src_cmpt = mp.Ez
            
            sources = [mp.Source(
                mp.GaussianSource(fcen, fwidth=0.1*fcen),
                component=src_cmpt,
                center=src_pt,
                size=mp.Vector3(0, 0, 0)
            )]
            
            # 4. �nPML�L
            pml_layers = [mp.PML(self.config.PML_THICKNESS)]
            
            # 5. �n!�h
            sim = mp.Simulation(
                cell_size=cell_size,
                geometry=geometry,
                sources=sources,
                boundary_layers=pml_layers,
                dimensions=1,  # 1D!�
                resolution=self.config.RESOLUTION
            )
            
            # 6. �����h
            refl_pt = mp.Vector3(0, 0, -0.25 * cell_size_z)  # ��Mn
            tran_pt = mp.Vector3(0, 0, 0.25 * cell_size_z)   # �Mn
            
            refl_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=refl_pt))
            tran_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=tran_pt))
            
            # 7. �L!�
            sim.run(until_after_sources=mp.stop_when_fields_decayed(20, mp.Ez, refl_pt, 1e-3))
            
            # 8. ������
            incident_flux = mp.get_fluxes(refl_flux)[0]
            reflected_flux = mp.get_fluxes(refl_flux)[0]
            transmitted_flux = mp.get_fluxes(tran_flux)[0]
            
            # ���� ��e�
            R = -reflected_flux / incident_flux
            T = transmitted_flux / incident_flux
            
            return max(0, min(1, R)), max(0, min(1, T))
        
        except Exception as e:
            print(f"Meep simulation failed: {str(e)}")
            return 0.0, 0.0  # ��ؤ<
    
       def simulate_validation_structure(self, structure_type, wavelength):
        """
        gL��ӄ!�
        
        �p:
            structure_type: ӄ{� ('Al2O3', 'TiN', 'Hybrid')
            wavelength: �(nm)
            
        ��:
            R, T: ����
        """
        wavelength_um = wavelength * 1e-3  # lb:�s
        
        try:
            # 1. ��Uӄ
            # ;ئ��: PML(0.3�m) + I�B(0.5�m) + ӄB(0.045�m) + �KhB(0.5�m) + PML(0.3�m)
            total_height = 0.3 + 0.5 + 0.045 + 0.5 + 0.3  # �m
            
            # 2. �n��:�
            cell_size = mp.Vector3(1.0, 0, total_height)  # 1D!�x�1�m
            
            # 3. P��I
            air = mp.Medium(epsilon=1.0)
            if structure_type == 'Al2O3':
                structure_medium = self.material_db.get_medium('Al2O3', wavelength_um)
            elif structure_type == 'TiN':
                structure_medium = self.material_db.get_medium('TiN_4nm', wavelength_um)
            else:  # Hybrid
                # �ӄ: 
J�Al2O3, J�TiN
                structure_medium1 = self.material_db.get_medium('Al2O3', wavelength_um)
                structure_medium2 = self.material_db.get_medium('TiN_4nm', wavelength_um)
            
            # 4. ��Uӄh
            geometry = []
            current_z = -total_height/2  # Ε� �
            
            # ��PMLB (0.3�m)
            pml_bottom = mp.Block(
                size=mp.Vector3(1.0, 0, 0.3),
                center=mp.Vector3(0, 0, current_z + 0.3/2),
                material=air
            )
            geometry.append(pml_bottom)
            current_z += 0.3
            
            # �KhB (0.5�m)
            tran_detector = mp.Block(
                size=mp.Vector3(1.0, 0, 0.5),
                center=mp.Vector3(0, 0, current_z + 0.5/2),
                material=air
            )
            geometry.append(tran_detector)
            current_z += 0.5
            
            # ӄB (0.045�m)
            if structure_type == 'Hybrid':
                # 
J�Al2O3 (0.0225�m)
                geometry.append(mp.Block(
                    size=mp.Vector3(1.0, 0, 0.0225),
                    center=mp.Vector3(0, 0, current_z + 0.0225/2),
                    material=structure_medium1
                ))
                # J�TiN (0.0225�m)
                geometry.append(mp.Block(
                    size=mp.Vector3(1.0, 0, 0.0225),
                    center=mp.Vector3(0, 0, current_z + 0.0225 + 0.0225/2),
                    material=structure_medium2
                ))
                current_z += 0.045
            else:
                structure_layer = mp.Block(
                    size=mp.Vector3(1.0, 0, 0.045),
                    center=mp.Vector3(0, 0, current_z + 0.045/2),
                    material=structure_medium
                )
                geometry.append(structure_layer)
                current_z += 0.045
            
            # I�B (0.5�m)
            source_layer = mp.Block(
                size=mp.Vector3(1.0, 0, 0.5),
                center=mp.Vector3(0, 0, current_z + 0.5/2),
                material=air
            )
            geometry.append(source_layer)
            current_z += 0.5
            
            # v�PMLB (0.3�m)
            pml_top = mp.Block(
                size=mp.Vector3(1.0, 0, 0.3),
                center=mp.Vector3(0, 0, current_z + 0.3/2),
                material=air
            )
            geometry.append(pml_top)
            
            # 5. �nI�
            fcen = 1 / wavelength_um  # -Ñ�
            src_pt = mp.Vector3(0, 0, -total_height/2 + 0.3 + 0.5/2)  # �Mn(I�B-�
            
            # (sO/
            src_cmpt = mp.Ex
            
            sources = [mp.Source(
                mp.GaussianSource(fcen, fwidth=0.1*fcen),
                component=src_cmpt,
                center=src_pt,
                size=mp.Vector3(0, 0, 0)
            )]
            
            # 6. �nPML�L
            pml_layers = [mp.PML(self.config.PML_THICKNESS)]
            
            # 7. �n!�h
            sim = mp.Simulation(
                cell_size=cell_size,
                geometry=geometry,
                sources=sources,
                boundary_layers=pml_layers,
                dimensions=1,  # 1D!�
                resolution=self.config.RESOLUTION
            )
            
            # 8. �����h
            # ��Mn: (I�B�
            refl_pt = mp.Vector3(0, 0, -total_height/2 + 0.3 + 0.5/2 - 0.1)
            # �Mn: (�KhB
�
            tran_pt = mp.Vector3(0, 0, -total_height/2 + 0.3 + 0.5/2 + 0.1)
            
            refl_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=refl_pt))
            tran_flux = sim.add_flux(fcen, 0, 1, mp.FluxRegion(center=tran_pt))
            
            # 9. �L!�
            sim.run(until_after_sources=mp.stop_when_fields_decayed(20, mp.Ez, refl_pt, 1e-3))
            
            # 10. ������
            incident_flux = mp.get_fluxes(refl_flux)[0]
            reflected_flux = np.abs(refl_flux.total_flux())[0]
            transmitted_flux = np.abs(tran_flux.total_flux())[0]
            
            # ���� ��e�
            R = reflected_flux / incident_flux
            T = transmitted_flux / incident_flux
            
            return max(0, min(1, R)), max(0, min(1, T))
        
        except Exception as e:
            print(f"Meep validation simulation failed: {str(e)}")
            return 0.0, 0.0  # ��ؤ<
# ====================== ��!W ======================
class ValidationModule:
    """
    ��!W
    ����Meep FDTD!�ӜTMM��Ӝ/& �
    
    ^':
        material_db: P�pn���
        config: Mn�a
        simulator: Meep!�h
        
    ��:
        calculate_tmm: (TMM������
        run_validation: gL��A
    """
    
    def __init__(self, material_db, config):
        self.material_db = material_db
        self.config = config
        self.simulator = MeepSimulator(material_db, config)
    
    def calculate_tmm(self, structure_type, wavelength):
        """
        (TMM������
        
        �p:
            structure_type: ӄ{� ('Al2O3', 'TiN', 'Hybrid')
            wavelength: �(nm)
            
        ��:
            R_tmm, T_tmm: TMM�������
        """
        wavelength_um = wavelength * 1e-3  # lb:�s
        
        # ��P�If8p
        n_air = 1.0
        k_air = 0.0
        
        if structure_type == 'Al2O3':
            n1, k1 = self.material_db.get_nk('Al2O3', wavelength_um)
            layers = [
                {'d': 0.045, 'n': n1, 'k': k1}  # 45nm Al2O3
            ]
        elif structure_type == 'TiN':
            n1, k1 = self.material_db.get_nk('TiN_4nm', wavelength�_um)
            layers = [
                {'d': 0.045, 'n': n1, 'k': k1}  # 45nm TiN
            ]
        else:  # Hybrid
            n1, k1 = self.material_db.get_nk('Al2O3', wavelength_um)
            n2, k2 = self.material_db.get_nk('TiN_4nm', wavelength_um)
            layers = [
                {'d': 0.0225, 'n': n1, 'k': k1},  # 22.5nm Al2O3
                {'d': 0.0225, 'n': n2, 'k': k2}   # 22.5nm TiN
            ]
        
        # �TMMӄ
        n_list = [n_air] + [layer['n'] for layer in layers] + [n_air]
        k_list = [k_air] + [layer['k'] for layer in layers] + [k_air]
        d_list = [0] + [layer['d'] for layer in layers] + [0]  # ��(�m)
        
        # ��e�('�)
        angle_rad = np.radians(self.config.VALIDATION_ANGLE)
        
        # ��O/�
        if self.config.VALIDATION_POLARIZATION == 's':
            pol = 's'
        else:
            pol = 'p'
        
        # (TMM��
        result = tmm.coh_tmm(
            pol, n_list, d_list, k_list, angle_rad, wavelength_um
        )
        
        R_tmm = result['R']
        T_tmm = result['T']
        
        return R_tmm, T_tmm
    
    def run_validation(self):
        """
        gL��A
        
        ��:
            bool: ��/&�
        """
        print("="*50)
        print(" ˌ�!W: ԃMeep FDTDTMM��Ӝ")
        print("="*50)
        
        structures = ['Al2O3', 'TiN', 'Hybrid']
        results = []
        validation_passed = True
        
        for struct in structures:
            print(f"\n��ӄ: {struct}")
            struct_results = []
            
            for wavelength in self.config.VALIDATION_WAVELENGTHS:
                # Meep!�
                R_meep, T_meep = self.simulator.simulate_validation_structure(struct, wavelength)
                
                # TMM��
                R_tmm, T_tmm = self.calculate_tmm(struct, wavelength)
                
                # ���
                diff_R = abs(R_meep - R_tmm)
                diff_T = abs(T_meep - T_tmm)
                
                # ��/&�<
                threshold = 0.05  # 5%�<
                passed = (diff_R < threshold) and (diff_T < threshold)
                
                if not passed:
                    validation_passed = False
                
                # �XӜ
                struct_results.append({
                    'wavelength': wavelength,
                    'R_meep': R_meep,
                    'T_meep': T_meep,
                    'R_tmm': R_tmm,
                    'T_tmm': T_tmm,
                    'diff_R': diff_R,
                    'diff_T': diff_T,
                    'passed': passed
                })
                
                print(f"� {wavelength}nm: "
                      f"Meep R={R_meep:.4f}, T={T_meep:.4f} | "
                      f"TMM R={R_tmm:.4f}, T={T_tmm:.4f} | "
                      f"� R={diff_R:.4f}, T={diff_T:.4f} | "
                      f"{'�' if passed else '1%'}")
            
            results.append({
                'structure': struct,
                'results': struct_results
            })
        
        # �X��Ӝ
        self.save_validation_results(results)
        
        # ����Ӝ
        self.visualize_validation_results(results)
        
        print("\n��Ӝ:", "�" if validation_passed else "1%")
        print("="*50)
        
        return validation_passed
    
    def save_validation_results(self, results):
        """�X��Ӝ0��"""
        all_data = []
        for struct_data in results:
            struct = struct_data['structure']
            for res in struct_data['results']:
                all_data.append({
                    'structure': struct,
                    'wavelength': res['wavelength'],
                    'R_meep': res['R_meep'],
                    'T_meep': res['T_meep'],
                    'R_tmm': res['R_tmm'],
                    'T_tmm': res['T_tmm'],
                    'diff_R': res['diff_R'],
                    'diff_T': res['diff_T'],
                    'passed': res['passed']
                })
        
        df = pd.DataFrame(all_data)
        df.to_csv(f"{self.config.OUTPUT_DIR}/validation_results.csv", index=False)
    
    def visualize_validation_results(self, results):
        """����Ӝ"""
        plt.figure(figsize=(15, 10))
        
        for i, struct_data in enumerate(results):
            struct = struct_data['structure']
            wavelengths = [res['wavelength'] for res in struct_data['results']]
            R_meep = [res['R_meep'] for res in struct_data['results']]
            T_meep = [res['T_meep'] for res in struct_data['results']]
            R_tmm = [res['R_tmm'] for res in struct_data['results']]
            T_tmm = [res['T_tmm'] for res in struct_data['results']]
            
            # Rԃ
            plt.subplot(2, 3, i+1)
            plt.plot(wavelengths, R_meep, 'bo-', label='Meep R')
            plt.plot(wavelengths, R_tmm, 'r--', label='TMM R')
            plt.title(f'{struct}ӄ - ��ԃ')
            plt.xlabel('� (nm)')
            plt.ylabel('��')
            plt.legend()
            plt.grid(True)
            
            # Tԃ
            plt.subplot(2, 3, i+4)
            plt.plot(wavelengths, T_meep, 'go-', label='Meep T')
            plt.plot(wavelengths, T_tmm, 'm--', label='TMM T')
            plt.title(f'{struct}ӄ - �ԃ')
            plt.xlabel('� (nm)')
            plt.ylabel('�')
            plt.legend()
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(f"{self.config.OUTPUT_DIR}/validation_comparison.png", dpi=150)
        plt.close()

# ====================== P�h ======================
class MaterialOptimizer:
    """
    P�h
    ��tP���!��K��
    
    ^':
        config: Mn�a
        material_db: P�pn�
        simulator: Meep!�h
        material_dist: P�
        nn_model: ^�Q�!�
        loss_history: _1��U
        
    ��:
        create_loss_matrix: ��*:߄_1�5
        evaluate_distribution: �0SM�_1
        neural_network_setup: �n^�Q�
        optimize: ;��
    """
    
    def __init__(self, config, material_db, simulator, material_dist):
        self.config = config
        self.material_db = material_db
        self.simulator = simulator
        self.material_dist = material_dist
        self.loss_history = []
        self.neural_network_setup()
    
    def create_loss_matrix(self):
        """
        �:�_1�5
        ��!�Ӝ��pn�����*:߄_1C�
        
        ��:
            loss_matrix: �*:߄_1<
        """
        # ��_1��G�@	:�!.sG
        num_regions = len(self.material_dist.region_materials)
        loss_matrix = np.ones(num_regions) * 0.5  # �_1<
        
        # �
B�����9n:�P�{��MnMC�
        # ��`�hb�:�q��'
        for region_id, material in self.material_dist.region_materials.items():
            # 9nMn�C`�hb�:�_1C��'
            min_z, max_z = float('inf'), float('-inf')
            for i in range(self.config.GRID_SIZE[0]):
                for j in range(self.config.GRID_SIZE[1]):
                    for k in range(self.config.GRID_SIZE[2]):
                        if self.material_dist.grid[i, j, k][1] == region_id:
                            if k < min_z:
                                min_z = k
                            if k > max_z:
                                max_z = k
            
            # zMnC�k<��`�hb	C͊'
            pos_weight = 1.0 - (min_z / self.config.GRID_SIZE[2])
            loss_matrix[region_id] *= pos_weight
            
        return loss_matrix
    
    def evaluate_distribution(self, wavelength, angle, polarization, R_sim, T_sim):
        """
        �0SMP��(�
        
        �p:
            wavelength: �(nm)
            angle: eҦ(�)
            polarization: O/�
            R_s�im: !���
            T_sim: !��
            
        ��:
            loss: �_1<
        """
        # �֞�R,Tpn
        if polarization == 's':
            R_meas, T_meas = self.material_db.get_interpolated_RT('TiN_4nm', wavelength)
        else: # pO/
            R_meas, T_meas = self.material_db.get_interpolated_RT('Al2O3', wavelength)
        
        # ��_1
        if R_meas is None or T_meas is None:
            return float('inf')
        
        loss_R = abs(R_sim - R_meas)
        loss_T = abs(T_sim - T_meas)
        
        # �_1
        return 0.7 * loss_R + 0.3 * loss_T
    
    def neural_network_setup(self):
        """
        �n^�Q�!�(��K_1
        
        Q�ӄ:
            �e: (�, Ҧ, O/, P�y�)
            ��: �K�_1<
        """
        # ��U�^�Q�
        self.nn_model = MLPRegressor(
            hidden_layer_sizes=(100, 50, 20),
            activation='relu',
            solver='adam',
            max_iter=1000,
            random_state=42
        )
        
        # �Z߭�pn
        X_train = np.random.rand(10, 5)  # 10*7,5*y�
        y_train = np.random.rand(10)    # 10*�<
        self.nn_model.fit(X_train, y_train)
    
    def update_neural_network(self, wavelength, angle, polarization, distribution_features, loss):
        """
        (�pn��^�Q�
        
        �p:
            wavelength: �(nm)
            angle: eҦ(�)
            polarization: O/�
            distribution_features: P�y��
            loss: SM_1<
        """
        # �y��
        features = np.array([wavelength, angle, 1 if polarization == 's' else 0] + distribution_features)
        
        # lb:^�Qܓe<
        X = features.reshape(1, -1)
        y = np.array([loss])
        
        # ��
        self.nn_model.partial_fit(X, y)
    
    def optimize(self):
        """
        ;��
        (��tP�
        
        A:
            1. M�@	�Ҧ�O/�
            2. �0SMP�
            3. �_1�5
            4. P�
            5. �X-�Ӝ
        """
        print(" �P�...")
        start_time = time.time()
        
        for iteration in range(self.config.MAX_ITER):
            iteration_start = time.time()
            total_loss = 0
            eval_count = 0
            print(f"\n�� {iteration+1}/{self.config.MAX_ITER}")
            
            # ��Xpn
            results = []
            
            for wavelength in tqdm(self.config.WAVELENGTHS, desc="�!�"):
                for angle in self.config.ANGLES:
                    for pol in self.config.POLARIZATIONS:
                        # (SM�L!�
                        R_sim, T_sim = self.simulator.simulate(
                            self.material_dist, wavelength, pol, angle
                        )
                        
                        # �0_1
                        loss = self.evaluate_distribution(wavelength, angle, pol, R_sim, T_sim)
                        total_loss += loss
                        eval_count += 1
                        
                        # �XӜ
                        results.append({
                            'wavelength': wavelength,
                            'angle': angle,
                            'polarization': pol,
                            'R_sim': R_sim,
                            'T_sim': T_sim,
                            'loss': loss
                        })
            
            # ��sG_1
            avg_loss = total_loss / eval_count if eval_count > 0 else float('inf')
            self.loss_history.append(avg_loss)
            
            # ��P�
            loss_matrix = self.create_loss_matrix()
            self.material_dist.optimize_distribution(
                loss_matrix, 
                learning_rate=self.config.LEARNING_RATE,
                mutation_rate=self.config.MUTATION_RATE
            )
            
            # �Xۦ
            if (iteration + 1) % self.config.SAVE_INTERVAL == 0:
                self.save_results(iteration, results)
                # ��-�
                self.material_dist.visualize_slice(
                    50, 'z', 
                    f"{self.config.OUTPUT_DIR}/distribution_iter_{iteration+1}.png"
                )
            
            iteration_time = time.time() - iteration_start
            print(f"�� {iteration+1} � - sG_1: {avg_loss:.6f} - �: {iteration_time:.2f}�")
        
        total_time = time.time() - start_time
        print(f"�! ;�: {total_time:.2f}�")
        
        # �X �Ӝ
        self.save_final_results()
        
        # �� �
        self.material_dist.visualize_slice(50, 'z', 
                                          f"{self.config.OUTPUT_DIR}/final_distribution.png")
    
    def save_results(self, iteration, results):
        """�X��Ӝ0��"""
        df = pd.DataFrame(results)
        df.to_csv(f"{self.config.OUTPUT_DIR}/iteration_{iteration+1}_results.csv", index=False)
        
        # �X_1��
        loss_df = pd.DataFrame({
            'iteration': range(1, iteration+2),
            'loss': self.loss_history
        })
        loss_df.to_csv(f"{self.config.OUTPUT_DIR}/loss_history.csv", index=False)
        
        # ���
        plt.figure(figsize=(10, 6))
        plt.plot(loss_df['iteration'], loss_df['loss'], 'o-')
        plt.xlabel('��!p')
        plt.ylabel('_1')
        plt.title('�_1�')
        plt.grid(True)
        plt.save�(f"{self.config.OUTPUT_DIR}/loss_history.png", dpi=150)
        plt.close()
    
    def save_final_results(self):
        """�X �P���o"""
        # �X �P�
        dist_data = []
        for i in range(self.config.GRID_SIZE[0]):
            for j in range(self.config.GRID_SIZE[1]):
                for k in range(self.config.GRID_SIZE[2]):
                    material, region = self.material_dist.grid[i, j, k]
                    dist_data.append({'x': i, 'y': j, 'z': k, 'material': material, 'region': region})
        
        dist_df = pd.DataFrame(dist_data)
        dist_df.to_csv(f"{self.config.OUTPUT_DIR}/final_distribution.csv", index=False)
        
        # �X �P�ߡ
        material_counts = defaultdict(int)
        for _, row in dist_df.iterrows():
            material_counts[row['material']] += 1
        
        stats_df = pd.DataFrame({
            'material': list(material_counts.keys()),
            'count': list(material_counts.values()),
            'percentage': [c / len(dist_df) * 100 for c in material_counts.values()]
        })
        stats_df.to_csv(f"{self.config.OUTPUT_DIR}/material_stats.csv", index=False)
        
        # ��P�ߡ
        plt.figure(figsize=(10, 6))
        stats_df.plot(kind='bar', x='material', y='percentage', legend=False)
        plt.ylabel('~� (%)')
        plt.title(' �P�ԋ')
        plt.xticks(rotation=45)
        plt.grid(axis='y')
        plt.tight_layout()
        plt.savefig(f"{self.config.OUTPUT_DIR}/material_distribution.png", dpi=150)
        plt.close()

# ====================== ;� ======================
def main():
    # �Mn
    config = Config()
    
    # �P�pn�
    material_db = MaterialDatabase(config)
    
    # �L��!W
    validator = ValidationModule(material_db, config)
    validation_passed = validator.run_validation()
    
    if not validation_passed:
        print("��1%! ���n�P�pn")
        return
    
    print("���!  �;�...")
    
    # �P�
    material_dist = MaterialDistribution(config)
    
    # �Meep!�h
    simulator = MeepSimulator(material_db, config)
    
    # �h
    optimizer = MaterialOptimizer(config, material_db, simulator, material_dist)
    
    # ���
    material_dist.visualize_slice(50, 'z', 
                                 f"{config.OUTPUT_DIR}/initial_distribution.png")
    
    #  �
    optimizer.optimize()
    
    print("@	Ӝ��X�:", config.OUTPUT_DIR)

if __name__ == "__main__":
    # ��Meep��
    try:
        mp.check_meep()
        print("Meep��cn ˡ�...")
        main()
    except Exception as e:
        print(f"Meep��1%: {e}")
        print("�n��cn��Meep:")
        print("1. �conda��: conda create -n pymeep python=3.9")
        print("2. �;��: conda activate pymeep")
        print("3. ��PyMeep: conda install -c conda-forge pymeep")
        print("4. ��v֝V: conda install numpy pandas matplotlib scipy scikit-learn scikit-optimize networkx")
        print("5. ��TMM�: pip install tmm")