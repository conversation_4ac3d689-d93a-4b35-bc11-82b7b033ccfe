#!/usr/bin/env python3
"""
Quick test of GAN training (5 epochs only)
"""

from gan_meep_electromagnetic import Config, GANTrainer

def main():
    print("🚀 Quick GAN Test (5 epochs)")
    print("="*40)
    
    # Initialize configuration
    config = Config()
    config.NUM_EPOCHS = 5  # Short test
    
    # Create GAN trainer
    trainer = GANTrainer(config)
    
    # Run short training
    print(f"\nStarting quick test of {config.TARGET_THICKNESS}nm structure...")
    print(f"Materials: {config.MATERIALS}")
    
    best_structure = trainer.train_gan(num_epochs=5)
    
    if best_structure is not None:
        print(f"\n🎉 Quick test completed!")
        print(f"Best loss achieved: {trainer.training_history['best_loss']:.6f}")
    else:
        print("❌ No valid structure found")

if __name__ == "__main__":
    main()
