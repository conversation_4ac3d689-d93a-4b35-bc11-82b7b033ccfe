#!/usr/bin/env python3
"""
Test script to check MEEP and TMM functionality
"""

try:
    import meep as mp
    print("MEEP imported successfully")
    print(f"MEEP version: {mp.__version__ if hasattr(mp, '__version__') else 'unknown'}")
    print("Available MEEP attributes:")
    attrs = [attr for attr in dir(mp) if not attr.startswith('_')]
    print(attrs[:20])  # Show first 20 attributes
    
    # Check for Vector3 alternatives
    if hasattr(mp, 'Vector3'):
        print("✓ Vector3 found")
    elif hasattr(mp, 'vec'):
        print("✓ vec found (alternative to Vector3)")
    else:
        print("✗ No Vector3 or vec found")
        
except ImportError as e:
    print(f"Failed to import MEEP: {e}")

print("\n" + "="*50)

try:
    import tmm
    print("TMM imported successfully")
    print(f"TMM version: {tmm.__version__ if hasattr(tmm, '__version__') else 'unknown'}")
    
    # Check TMM function signature
    import inspect
    if hasattr(tmm, 'coh_tmm'):
        sig = inspect.signature(tmm.coh_tmm)
        print(f"coh_tmm signature: {sig}")
    else:
        print("✗ coh_tmm function not found")
        
except ImportError as e:
    print(f"Failed to import TMM: {e}")
