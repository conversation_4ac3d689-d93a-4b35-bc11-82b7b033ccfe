#!/usr/bin/env python3
"""
Simple MEEP Test - Debug the basic MEEP setup
"""

import numpy as np
import matplotlib.pyplot as plt
import time

try:
    import meep as mp
    MEEP_AVAILABLE = True
    print("✓ PyMEEP successfully imported")
except ImportError:
    MEEP_AVAILABLE = False
    print("❌ PyMEEP not available")
    exit(1)

def simple_meep_test():
    """Test basic MEEP functionality with a simple dielectric slab"""
    print("\n🔬 Simple MEEP Test - Dielectric Slab")
    print("="*50)
    
    # Parameters
    wavelength = 0.8  # 800nm in micrometers
    frequency = 1.0 / wavelength
    
    # Material properties (Al2O3)
    n = 1.76
    epsilon = n**2
    
    # Geometry
    slab_thickness = 0.045  # 45nm
    
    # Simulation cell
    pml_thickness = 1.0
    padding = 1.0
    cell_z = slab_thickness + 2*pml_thickness + 2*padding
    cell_x = 2.0
    cell_y = 2.0
    
    cell = mp.Vector3(cell_x, cell_y, cell_z)
    
    # PML layers
    pml_layers = [mp.PML(thickness=pml_thickness)]
    
    # Geometry - dielectric slab
    geometry = [mp.Block(size=mp.Vector3(cell_x, cell_y, slab_thickness),
                        center=mp.Vector3(0, 0, 0),
                        material=mp.Medium(epsilon=epsilon))]
    
    # Source
    source_z = -cell_z/2 + pml_thickness + padding/2
    sources = [mp.Source(mp.GaussianSource(frequency, fwidth=frequency*0.1),
                        component=mp.Ex,
                        center=mp.Vector3(0, 0, source_z))]
    
    # Create simulation
    sim = mp.Simulation(cell_size=cell,
                       boundary_layers=pml_layers,
                       geometry=geometry,
                       sources=sources,
                       resolution=20)
    
    # Flux monitors
    refl_z = source_z + padding/4
    tran_z = cell_z/2 - pml_thickness - padding/2
    
    print(f"Source at z = {source_z:.3f}")
    print(f"Reflection monitor at z = {refl_z:.3f}")
    print(f"Transmission monitor at z = {tran_z:.3f}")
    print(f"Slab center at z = 0.000")
    print(f"Slab thickness = {slab_thickness:.3f}")
    
    # Add flux monitors
    refl = sim.add_flux(frequency, 0, 1,
                       mp.FluxRegion(center=mp.Vector3(0, 0, refl_z),
                                   size=mp.Vector3(1.5, 1.5, 0)))
    
    tran = sim.add_flux(frequency, 0, 1,
                       mp.FluxRegion(center=mp.Vector3(0, 0, tran_z),
                                   size=mp.Vector3(1.5, 1.5, 0)))
    
    print("\nRunning simulation with structure...")
    start_time = time.time()
    
    # Run simulation
    sim.run(until_after_sources=mp.stop_when_fields_decayed(50, mp.Ex,
                                                           mp.Vector3(0, 0, tran_z), 1e-6))
    
    # Get fluxes
    refl_flux = mp.get_fluxes(refl)[0]
    tran_flux = mp.get_fluxes(tran)[0]
    
    print(f"With structure: refl_flux = {refl_flux:.6f}, tran_flux = {tran_flux:.6f}")
    
    # Reset and run reference simulation
    print("\nRunning reference simulation (no structure)...")
    sim.reset_meep()
    
    # Create reference simulation (no geometry)
    sim_ref = mp.Simulation(cell_size=cell,
                           boundary_layers=pml_layers,
                           geometry=[],  # No geometry
                           sources=sources,
                           resolution=20)
    
    # Add reference flux monitor
    refl_ref = sim_ref.add_flux(frequency, 0, 1,
                               mp.FluxRegion(center=mp.Vector3(0, 0, refl_z),
                                           size=mp.Vector3(1.5, 1.5, 0)))
    
    # Run reference
    sim_ref.run(until_after_sources=mp.stop_when_fields_decayed(50, mp.Ex,
                                                               mp.Vector3(0, 0, refl_z), 1e-6))
    
    # Get reference flux
    incident_flux = mp.get_fluxes(refl_ref)[0]
    
    print(f"Reference: incident_flux = {incident_flux:.6f}")
    
    simulation_time = time.time() - start_time
    
    # Calculate R and T
    if incident_flux != 0:
        R = -refl_flux / incident_flux  # Negative because reflection is opposite direction
        T = tran_flux / incident_flux
    else:
        R = T = 0.0
    
    print(f"\nResults:")
    print(f"  R = {R:.6f}")
    print(f"  T = {T:.6f}")
    print(f"  A = {1-R-T:.6f}")
    print(f"  R + T + A = {R+T+(1-R-T):.6f}")
    print(f"  Simulation time: {simulation_time:.1f}s")
    
    # Compare with analytical result for normal incidence
    # Fresnel equations for air-dielectric interface
    r = (1 - n) / (1 + n)
    R_analytical = r**2
    
    # For thin film, approximate (ignoring interference)
    T_analytical = (1 - R_analytical)**2  # Simplified
    
    print(f"\nComparison with analytical (approximate):")
    print(f"  R_analytical ≈ {R_analytical:.6f}")
    print(f"  T_analytical ≈ {T_analytical:.6f}")
    print(f"  Difference: ΔR = {abs(R - R_analytical):.6f}, ΔT = {abs(T - T_analytical):.6f}")
    
    return R, T, R_analytical, T_analytical

def test_tmm_comparison():
    """Test TMM calculation for comparison"""
    print("\n🧮 TMM Calculation for Comparison")
    print("="*40)
    
    # Same parameters as MEEP test
    wavelength_um = 0.8
    n = 1.76
    d = 0.045  # thickness in micrometers
    
    # Simple TMM for single layer
    # Phase factor
    k0 = 2 * np.pi / wavelength_um
    beta = k0 * n * d
    
    # Fresnel coefficients (air-dielectric-air)
    r12 = (1 - n) / (1 + n)  # air to dielectric
    r21 = (n - 1) / (n + 1)  # dielectric to air
    t12 = 2 / (1 + n)
    t21 = 2 * n / (n + 1)
    
    # Transfer matrix method
    # For a single layer between two semi-infinite media
    r = (r12 + r21 * np.exp(2j * beta)) / (1 + r12 * r21 * np.exp(2j * beta))
    t = (t12 * t21 * np.exp(1j * beta)) / (1 + r12 * r21 * np.exp(2j * beta))
    
    R_tmm = abs(r)**2
    T_tmm = abs(t)**2
    
    print(f"TMM Results:")
    print(f"  R = {R_tmm:.6f}")
    print(f"  T = {T_tmm:.6f}")
    print(f"  A = {1-R_tmm-T_tmm:.6f}")
    
    return R_tmm, T_tmm

def main():
    """Main test function"""
    print("🔬 MEEP Verification - Simple Test")
    print("="*50)
    
    if not MEEP_AVAILABLE:
        print("❌ PyMEEP not available!")
        return False
    
    # Run TMM calculation
    R_tmm, T_tmm = test_tmm_comparison()
    
    # Run MEEP simulation
    R_meep, T_meep, R_analytical, T_analytical = simple_meep_test()
    
    # Compare results
    print("\n" + "="*50)
    print("COMPARISON SUMMARY")
    print("="*50)
    
    print(f"TMM:        R = {R_tmm:.6f}, T = {T_tmm:.6f}")
    print(f"MEEP:       R = {R_meep:.6f}, T = {T_meep:.6f}")
    print(f"Analytical: R = {R_analytical:.6f}, T = {T_analytical:.6f}")
    
    # Calculate differences
    diff_R_tmm = abs(R_meep - R_tmm)
    diff_T_tmm = abs(T_meep - T_tmm)
    rel_diff_R = diff_R_tmm / max(R_tmm, 1e-10) * 100
    rel_diff_T = diff_T_tmm / max(T_tmm, 1e-10) * 100
    
    print(f"\nMEEP vs TMM:")
    print(f"  ΔR = {diff_R_tmm:.6f} ({rel_diff_R:.2f}%)")
    print(f"  ΔT = {diff_T_tmm:.6f} ({rel_diff_T:.2f}%)")
    
    # Verification
    tolerance = 10.0  # 10% tolerance for this simple test
    passes = (rel_diff_R < tolerance) and (rel_diff_T < tolerance)
    
    if passes:
        print(f"\n✅ VERIFICATION PASSED!")
        print(f"   MEEP implementation is working correctly")
        return True
    else:
        print(f"\n❌ VERIFICATION FAILED!")
        print(f"   MEEP implementation needs debugging")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 Ready to proceed with full verification and GAN optimization")
    else:
        print("\n⚠ Fix MEEP implementation before proceeding")
