#!/usr/bin/env python3
"""
Test script for GAN electromagnetic simulation
Tests the implementation without requiring full PyMEEP installation
"""

import numpy as np
import torch
import pandas as pd
import os
from gan_meep_electromagnetic import Config, MaterialDatabase, ExperimentalDataLoader, GANTrainer

def create_test_data():
    """Create test experimental data files"""
    print("Creating test experimental data...")
    
    os.makedirs("data", exist_ok=True)
    
    # Create test experimental data for different angles
    wavelengths = np.linspace(300, 2600, 100)
    
    for angle in [15, 30, 45, 60]:
        # Simulate realistic R/T data with some angle dependence
        R = 0.2 + 0.1 * np.sin(wavelengths / 500) + 0.05 * np.random.random(len(wavelengths))
        T = 0.3 + 0.1 * np.cos(wavelengths / 600) + 0.05 * np.random.random(len(wavelengths))
        
        # Ensure physical constraints
        R = np.clip(R, 0, 1)
        T = np.clip(T, 0, 1-R)
        
        # Save to CSV
        data = pd.DataFrame({
            'wavelength': wavelengths,
            'R': R,
            'T': T
        })
        data.to_csv(f"data/RT_{angle}degree_SP.csv", index=False)
        print(f"✓ Created data/RT_{angle}degree_SP.csv")

def test_material_database():
    """Test material database functionality"""
    print("\nTesting Material Database...")
    
    config = Config()
    material_db = MaterialDatabase(config)
    
    # Test material loading
    for material in config.MATERIALS:
        try:
            n, k = material_db.get_nk(material, 0.8)  # 800nm
            print(f"✓ {material}: n={n:.3f}, k={k:.3f} at 800nm")
        except Exception as e:
            print(f"❌ Error with {material}: {e}")

def test_experimental_data():
    """Test experimental data loading"""
    print("\nTesting Experimental Data Loader...")
    
    config = Config()
    exp_data = ExperimentalDataLoader(config)
    
    # Test data retrieval
    for angle in config.INCIDENT_ANGLES:
        try:
            R, T = exp_data.get_rt_at_wavelength(angle, 800)  # 800nm
            print(f"✓ {angle}°: R={R:.3f}, T={T:.3f} at 800nm")
        except Exception as e:
            print(f"❌ Error with {angle}°: {e}")

def test_gan_networks():
    """Test GAN network architectures"""
    print("\nTesting GAN Networks...")
    
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Test Generator
    from gan_meep_electromagnetic import Generator, Discriminator, SurrogateSimulator
    
    generator = Generator(
        latent_dim=config.LATENT_DIM,
        num_materials=len(config.MATERIALS),
        grid_size=config.GRID_SIZE
    ).to(device)
    
    # Test generator forward pass
    noise = torch.randn(2, config.LATENT_DIM, device=device)
    fake_structures = generator(noise)
    print(f"✓ Generator output shape: {fake_structures.shape}")
    print(f"  Expected: (2, {len(config.MATERIALS)}, {config.GRID_SIZE[0]}, {config.GRID_SIZE[1]}, {config.GRID_SIZE[2]})")
    
    # Test Discriminator
    discriminator = Discriminator(
        num_materials=len(config.MATERIALS),
        grid_size=config.GRID_SIZE
    ).to(device)
    
    predictions = discriminator(fake_structures)
    print(f"✓ Discriminator output shape: {predictions.shape}")
    print(f"  Predictions: {predictions.squeeze().detach().cpu().numpy()}")
    
    # Test Surrogate Simulator
    surrogate = SurrogateSimulator(
        num_materials=len(config.MATERIALS),
        grid_size=config.GRID_SIZE
    ).to(device)
    
    # Test surrogate forward pass
    wavelengths = torch.tensor([[0.8], [1.2]], device=device)  # 800nm, 1200nm
    angles = torch.tensor([[30.0], [45.0]], device=device)
    polarizations = torch.zeros(2, 1, device=device)
    
    R_pred, T_pred = surrogate(fake_structures, wavelengths, angles, polarizations)
    print(f"✓ Surrogate Simulator R shape: {R_pred.shape}, T shape: {T_pred.shape}")
    print(f"  R predictions: {R_pred.squeeze().detach().cpu().numpy()}")
    print(f"  T predictions: {T_pred.squeeze().detach().cpu().numpy()}")

def test_training_setup():
    """Test GAN training setup (without full training)"""
    print("\nTesting GAN Training Setup...")
    
    config = Config()
    trainer = GANTrainer(config)
    
    # Test real pattern generation
    real_patterns = trainer.generate_real_patterns(batch_size=4)
    print(f"✓ Real patterns shape: {real_patterns.shape}")
    
    # Test physics loss calculation
    fake_patterns = trainer.generator(torch.randn(4, config.LATENT_DIM, device=trainer.device))
    physics_loss = trainer.calculate_physics_loss(fake_patterns)
    print(f"✓ Physics loss: {physics_loss.item():.6f}")
    
    print("✓ Training setup successful!")

def main():
    """Run all tests"""
    print("🧪 Testing GAN Electromagnetic Simulation Implementation")
    print("="*60)
    
    try:
        # Create test data
        create_test_data()
        
        # Run tests
        test_material_database()
        test_experimental_data()
        test_gan_networks()
        test_training_setup()
        
        print("\n" + "="*60)
        print("✅ All tests passed!")
        print("\nTo run full training:")
        print("  python gan_meep_electromagnetic.py")
        print("\nTo install PyMEEP for real electromagnetic simulation:")
        print("  conda install -c conda-forge pymeep")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
